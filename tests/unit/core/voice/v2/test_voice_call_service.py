"""Unit tests for VoiceCallService."""

from datetime import UTC, datetime
from typing import Literal
from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch
from uuid import UUID, uuid4

import pytest
from temporalio.exceptions import WorkflowAlreadyStartedError

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.core.activity.types import ActivityPatchRequest
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.voice.service_type import Twi<PERSON>VoiceToken
from salestech_be.core.voice.v2.types import (
    OrganizationPhoneNumberAssignment,
    VoiceResponse,
    VoiceTokenResponse,
)
from salestech_be.core.voice.v2.voice_call_service import (
    VOICE_TASK_QUEUE,
    VoiceCallFollowUpEmailWorkflow,
    VoiceCallService,
    WorkflowIDReusePolicy,
)
from salestech_be.db.models.activity import (
    ActivitySubType,
)
from salestech_be.db.models.domain_crm_association import (
    DomainCRMAssociation,
    DomainType,
)
from salestech_be.db.models.meeting import (
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.db.models.notification import (
    NotificationReferenceIdType,
    NotificationVoiceCallData,
    NotificationWorkflow,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
)
from salestech_be.db.models.voice_v2 import (
    Call,
    CallDirection,
    CallDisposition,
    CallStatus,
    CallType,
    VoiceProvider,
    VoiceProviderAccount,
    VoiceProviderAccountStatus,
    VoiceUsageCategory,
)
from salestech_be.integrations.twilio.type import UsageRecordsResponse
from salestech_be.services.auth.encryptions import fernet_encryption_manager
from salestech_be.settings import settings
from salestech_be.temporal.workflows.voice.types import (
    VoiceCallFollowUpEmailWorkflowInput,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.meeting.schema import (
    CreateMeetingRequest,
    PatchMeetingRequest,
)
from salestech_be.web.api.organization.schema import (
    FollowupEmailPreferences as OrganizationFollowupEmailPreferences,
)
from salestech_be.web.api.organization.schema import (
    OrganizationPreferenceDomainVoice,
    OrganizationPreferenceResponse,
)
from salestech_be.web.api.user.schema import (
    CallForwardingPreferences,
    UserPreferenceDomainVoice,
    UserPreferenceKeys,
    UserPreferenceResponse,
    VoiceMailPreferences,
)
from salestech_be.web.api.user.schema import (
    FollowupEmailPreferences as UserFollowupEmailPreferences,
)
from salestech_be.web.api.voice.v2.schema import (
    InitiateCallRequest,
    UpdateCallRecordingRequest,
    UpdateCallRecordingStatus,
    UpdateCallRequest,
)

TEST_DATETIME = zoned_utc_now()


@pytest.fixture
def voice_provider_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def voice_phone_number_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def contact_query_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def activity_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def voice_call_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def provider_client() -> AsyncMock:
    client = AsyncMock()
    client.create_voice_token.return_value = Mock()
    return client


@pytest.fixture
def call_recording_s3_manager() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def voicemail_greeting_s3_manager() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def task_v2_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def meeting_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def notification_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_preference_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def organization_preference_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def ff_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def email_account_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def domain_crm_association_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def quota_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def user_phone_number_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def service(
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    contact_query_service: AsyncMock,
    activity_service: AsyncMock,
    voice_call_repository: AsyncMock,
    provider_client: AsyncMock,
    user_service: AsyncMock,
    call_recording_s3_manager: AsyncMock,
    voicemail_greeting_s3_manager: AsyncMock,
    meeting_service: AsyncMock,
    notification_service: AsyncMock,
    ff_service: AsyncMock,
    email_account_service: AsyncMock,
    user_preference_service: AsyncMock,
    organization_preference_service: AsyncMock,
    task_v2_service: AsyncMock,
    domain_crm_association_service: AsyncMock,
    quota_service: AsyncMock,
    user_phone_number_service: AsyncMock,
) -> VoiceCallService:
    service = VoiceCallService(
        voice_provider_repository=voice_provider_repository,
        voice_phone_number_repository=voice_phone_number_repository,
        contact_query_service=contact_query_service,
        activity_service=activity_service,
        voice_call_repository=voice_call_repository,
        user_service=user_service,
        call_recording_s3_manager=call_recording_s3_manager,
        voicemail_greeting_s3_manager=voicemail_greeting_s3_manager,
        meeting_service=meeting_service,
        notification_service=notification_service,
        ff_service=ff_service,
        email_account_service=email_account_service,
        user_preference_service=user_preference_service,
        organization_preference_service=organization_preference_service,
        task_v2_service=task_v2_service,
        domain_crm_association_service=domain_crm_association_service,
        quota_service=quota_service,
        user_phone_number_service=user_phone_number_service,
    )
    # Mock the provider_client method to return our mock client
    service._provider_client = lambda _: provider_client  # type: ignore
    return service


async def test_create_token_success(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    provider_client: AsyncMock,
) -> None:
    """Test creating a voice token successfully."""
    organization_id = uuid4()
    user_id = uuid4()
    provider = "twilio"
    token_type = "webrtc"  # noqa: S105
    api_secret = "test_secret"  # noqa: S105
    encrypted_secret = fernet_encryption_manager.encrypt(api_secret)
    twilio_token = TwilioVoiceToken(
        identity="test_identity",
        jwt_token="test_token",  # noqa: S106
    )
    expected_token = VoiceTokenResponse(
        identity=twilio_token.identity,
        token=twilio_token.jwt_token,
        type=token_type,
    )

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=uuid4(),
        organization_id=organization_id,
        provider=provider,
        external_id="ACaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        api_key="SKaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        api_secret=encrypted_secret,
        metadata={"application_sid": "APaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"},
        created_by_user_id=user_id,
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )
    voice_provider_repository.get_provider_account_by_organization_and_provider.return_value = provider_account

    # Mock provider client
    provider_client.create_voice_token = Mock(return_value=twilio_token)
    # Execute test
    token = await service.create_token(
        organization_id=organization_id,
        user_id=user_id,
        provider=provider,
        token_type=token_type,
    )

    # Verify result
    assert token == expected_token
    voice_provider_repository.get_provider_account_by_organization_and_provider.assert_called_once_with(
        organization_id=organization_id,
        provider=provider,
    )


async def test_create_token_no_provider_account(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
) -> None:
    """Test creating a voice token when no provider account exists."""
    organization_id = uuid4()
    user_id = uuid4()
    provider = "twilio"
    token_type = "webrtc"  # noqa: S105

    # Mock no provider account found
    voice_provider_repository.get_provider_account_by_organization_and_provider.return_value = None

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.create_token(
            organization_id=organization_id,
            user_id=user_id,
            provider=provider,
            token_type=token_type,
        )
    assert str(exc_info.value) == "No voice provider account found for organization"


async def test_handle_voice_call_outbound_with_extension(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    provider_client: AsyncMock,
    meeting_service: AsyncMock,
    voice_call_repository: AsyncMock,
    contact_query_service: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test handling a voice call with extension."""
    # API call params
    call_sid = "TEST_CALL_SID"
    to_number = "+***********"  # 1234 is extension
    to_extension = "1234"
    from_number = "+***********"
    from_extension = ""
    caller = "TEST_CALLER"
    direction = CallDirection.OUTBOUND
    account_sid = "TEST_ACCOUNT_SID"
    contact_id = uuid4()  # should be uuid
    pipeline_id = uuid4()  # should be uuid
    account_id = uuid4()

    organization_id = uuid4()
    provider_account_id = uuid4()
    org_phone_number_id = uuid4()
    user_id = uuid4()
    call_id = uuid4()

    api_secret = "test_secret"  # noqa: S105
    encrypted_secret = fernet_encryption_manager.encrypt(api_secret)

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id=account_sid,
        api_key="API_KEY",
        api_secret=encrypted_secret,
        metadata={"application_sid": uuid4()},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=org_phone_number_id,
        voice_provider_account_id=provider_account_id,
        call_type=CallType.WEBRTC,
        caller_number=from_number,
        status=CallStatus.INITIATED,
        direction=direction,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        recipient_number="+***********",
        recipient_id=contact_id,
        account_id=account_id,
        metadata={"call_sid": call_sid, "provider": provider_account.provider},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
        recipient_extension="1234",
    )

    domain_crm_association_contact = DomainCRMAssociation(
        id=uuid4(),
        organization_id=organization_id,
        domain_type=DomainType.VOICE_CALL,
        call_id=call_id,
        created_by_user_id=user_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        created_at=TEST_DATETIME,
    )

    domain_crm_association_user = DomainCRMAssociation(
        id=uuid4(),
        organization_id=organization_id,
        domain_type=DomainType.VOICE_CALL,
        call_id=call_id,
        user_id=user_id,
        created_by_user_id=user_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        created_at=TEST_DATETIME,
    )

    domain_crm_association_service.create_domain_crm_association.side_effect = [
        domain_crm_association_contact,
        domain_crm_association_user,
    ]

    # Set up mock returns
    voice_provider_repository.get_provider_account_by_external_id.return_value = (
        provider_account
    )

    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.return_value = OrganizationPhoneNumberAssignment(
        organization_phone_number_id=org_phone_number_id,
        assigned_to=user_id,
    )

    voice_call_repository.insert.return_value = call

    # Mock contact repository to return a contact with name
    contact_query_service.list_contact_ids_by_phone_number.return_value = Mock(
        contact_id
    )
    contact_query_service.get_by_id.return_value = Mock(
        id=contact_id, display_name="Test Contact"
    )

    # Mock meeting dto
    mock_meeting = Mock()
    mock_meeting.id = uuid4()
    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting
    meeting_service.create_meeting.return_value = mock_meeting_dto

    mock_response = VoiceResponse()
    provider_client.get_actual_call_direction = Mock(return_value=direction)
    provider_client.build_call_response = Mock(return_value=mock_response)

    # run method
    response = await service.handle_voice_call(
        call_sid=call_sid,
        to_number=to_number,
        recipient_extension=to_extension,
        from_number=from_number,
        caller_extension=from_extension,
        caller=caller,
        direction=direction,
        account_sid=account_sid,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
    )

    # assertions
    assert isinstance(response, VoiceResponse)
    assert response == mock_response

    voice_provider_repository.get_provider_account_by_external_id.assert_called_once_with(
        external_id=account_sid
    )

    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.assert_called_once_with(
        organization_id=organization_id,
        phone_number=from_number if direction == CallDirection.OUTBOUND else to_number,
    )

    provider_client.get_actual_call_direction.assert_called_once_with(caller, direction)
    provider_client.build_call_response.assert_called_once_with(
        call_sid=call_sid,
        from_number=from_number,
        to_number=to_number,
        user_id=user_id,
        display_caller_number=from_number,
        direction=direction,
        recipient_extension=to_extension,
    )

    # Verify call creation
    voice_call_repository.insert.assert_called_once()
    inserted_call = voice_call_repository.insert.call_args[0][0]
    assert isinstance(inserted_call, Call)
    assert inserted_call.organization_id == organization_id
    assert inserted_call.contact_id == contact_id
    assert inserted_call.external_id == call_sid

    # Verify meeting creation
    meeting_service.create_meeting.assert_called_once()
    create_meeting_args = meeting_service.create_meeting.call_args[1]

    # Verify organization and user IDs
    assert create_meeting_args["organization_id"] == organization_id
    assert create_meeting_args["user_id"] == user_id
    assert create_meeting_args["request"].account_id == account_id
    assert create_meeting_args["request"].platform == MeetingProvider.VOICE
    assert (
        create_meeting_args["request"].reference_id_type
        == MeetingReferenceIdType.VOICE_V2
    )
    assert create_meeting_args["request"].reference_id == call_id
    assert create_meeting_args["request"].starts_at == call.created_at
    assert create_meeting_args["request"].started_at == call.created_at
    assert (
        create_meeting_args["request"].title
        == "Call with Test Contact (+1 (647) 888-9999,1234)"
    )

    # Verify domain_crm_association creation
    assert domain_crm_association_service.create_domain_crm_association.call_count == 2

    # Get args for first call (contact association)
    first_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[0][
            0
        ][0]
    )
    assert first_call_args.organization_id == organization_id
    assert first_call_args.call_id == call.id
    assert first_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert first_call_args.contact_id == contact_id
    assert first_call_args.pipeline_id == pipeline_id
    assert first_call_args.account_id == account_id
    assert first_call_args.created_by_user_id == user_id
    assert first_call_args.user_id is None  # First call should not have user_id

    # Get args for second call (user association)
    second_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[1][
            0
        ][0]
    )
    assert second_call_args.organization_id == organization_id
    assert second_call_args.call_id == call.id
    assert second_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert second_call_args.contact_id is None  # Second call should not have contact_id
    assert second_call_args.pipeline_id == pipeline_id
    assert second_call_args.account_id == account_id
    assert second_call_args.created_by_user_id == user_id
    assert second_call_args.user_id == user_id


async def test_handle_voice_call_outbound_no_extension(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    provider_client: AsyncMock,
    meeting_service: AsyncMock,
    voice_call_repository: AsyncMock,
    contact_query_service: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test handling a voice call without extension."""
    # API call params
    call_sid = "TEST_CALL_SID"
    to_number = "+***********"
    to_extension = ""
    from_number = "+***********"
    from_extension = ""
    caller = "TEST_CALLER"
    direction = CallDirection.OUTBOUND
    account_sid = "TEST_ACCOUNT_SID"
    contact_id = uuid4()  # should be uuid
    pipeline_id = uuid4()  # should be uuid
    account_id = uuid4()

    organization_id = uuid4()
    provider_account_id = uuid4()
    org_phone_number_id = uuid4()
    user_id = uuid4()
    call_id = uuid4()

    api_secret = "test_secret"  # noqa: S105
    encrypted_secret = fernet_encryption_manager.encrypt(api_secret)

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id=account_sid,
        api_key="API_KEY",
        api_secret=encrypted_secret,
        metadata={"application_sid": uuid4()},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=org_phone_number_id,
        voice_provider_account_id=provider_account_id,
        call_type=CallType.WEBRTC,
        caller_number=from_number,
        status=CallStatus.INITIATED,
        direction=direction,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        recipient_number=to_number,
        recipient_id=contact_id,
        account_id=account_id,
        metadata={"call_sid": call_sid, "provider": provider_account.provider},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    domain_crm_association_contact = DomainCRMAssociation(
        id=uuid4(),
        organization_id=organization_id,
        domain_type=DomainType.VOICE_CALL,
        call_id=call_id,
        created_by_user_id=user_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        created_at=TEST_DATETIME,
    )

    domain_crm_association_user = DomainCRMAssociation(
        id=uuid4(),
        organization_id=organization_id,
        domain_type=DomainType.VOICE_CALL,
        call_id=call_id,
        user_id=user_id,
        created_by_user_id=user_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        created_at=TEST_DATETIME,
    )

    domain_crm_association_service.create_domain_crm_association.side_effect = [
        domain_crm_association_contact,
        domain_crm_association_user,
    ]

    # Set up mock returns
    voice_provider_repository.get_provider_account_by_external_id.return_value = (
        provider_account
    )

    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.return_value = OrganizationPhoneNumberAssignment(
        organization_phone_number_id=org_phone_number_id,
        assigned_to=user_id,
    )

    voice_call_repository.insert.return_value = call

    # Mock contact repository to return a contact with name
    contact_query_service.list_contact_ids_by_phone_number.return_value = Mock(
        contact_id
    )
    contact_query_service.get_by_id.return_value = Mock(
        id=contact_id, display_name="Test Contact"
    )

    # Mock meeting dto
    mock_meeting = Mock()
    mock_meeting.id = uuid4()
    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting
    meeting_service.create_meeting.return_value = mock_meeting_dto

    mock_response = VoiceResponse()
    provider_client.get_actual_call_direction = Mock(return_value=direction)
    provider_client.build_call_response = Mock(return_value=mock_response)

    # run method
    response = await service.handle_voice_call(
        call_sid=call_sid,
        to_number=to_number,
        from_number=from_number,
        caller=caller,
        direction=direction,
        account_sid=account_sid,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        caller_extension=from_extension,
        recipient_extension=to_extension,
    )

    # assertions
    assert isinstance(response, VoiceResponse)
    assert response == mock_response

    voice_provider_repository.get_provider_account_by_external_id.assert_called_once_with(
        external_id=account_sid
    )

    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.assert_called_once_with(
        organization_id=organization_id,
        phone_number=from_number if direction == CallDirection.OUTBOUND else to_number,
    )

    provider_client.get_actual_call_direction.assert_called_once_with(caller, direction)
    provider_client.build_call_response.assert_called_once_with(
        call_sid=call_sid,
        from_number=from_number,
        to_number=to_number,
        user_id=user_id,
        display_caller_number=from_number,
        direction=direction,
        recipient_extension=None,
    )

    # Verify call creation
    voice_call_repository.insert.assert_called_once()
    inserted_call = voice_call_repository.insert.call_args[0][0]
    assert isinstance(inserted_call, Call)
    assert inserted_call.organization_id == organization_id
    assert inserted_call.contact_id == contact_id
    assert inserted_call.external_id == call_sid

    # Verify meeting creation
    meeting_service.create_meeting.assert_called_once()
    create_meeting_args = meeting_service.create_meeting.call_args[1]

    # Verify organization and user IDs
    assert create_meeting_args["organization_id"] == organization_id
    assert create_meeting_args["user_id"] == user_id
    assert create_meeting_args["request"].account_id == account_id
    assert create_meeting_args["request"].platform == MeetingProvider.VOICE
    assert (
        create_meeting_args["request"].reference_id_type
        == MeetingReferenceIdType.VOICE_V2
    )
    assert create_meeting_args["request"].reference_id == call_id
    assert create_meeting_args["request"].starts_at == call.created_at
    assert create_meeting_args["request"].started_at == call.created_at
    assert (
        create_meeting_args["request"].title
        == "Call with Test Contact (+1 (647) 888-9999)"
    )

    # Verify meeting request
    meeting_request: CreateMeetingRequest = create_meeting_args["request"]
    assert meeting_request.platform == MeetingProvider.VOICE
    assert meeting_request.reference_id_type == MeetingReferenceIdType.VOICE_V2
    assert meeting_request.reference_id == call_id
    assert (
        meeting_request.title == "Call with Test Contact (+1 (647) 888-9999)"
    )  # Updated title format

    # Verify invitees
    assert len(meeting_request.invitees) == 2
    organizer = next(i for i in meeting_request.invitees if i.is_organizer)
    participant = next(i for i in meeting_request.invitees if not i.is_organizer)
    assert organizer.user_id == user_id
    assert participant.contact_id == contact_id

    # Verify domain_crm_association creation
    assert domain_crm_association_service.create_domain_crm_association.call_count == 2

    # Get args for first call (contact association)
    first_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[0][
            0
        ][0]
    )
    assert first_call_args.organization_id == organization_id
    assert first_call_args.call_id == call.id
    assert first_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert first_call_args.contact_id == contact_id
    assert first_call_args.pipeline_id == pipeline_id
    assert first_call_args.account_id == account_id
    assert first_call_args.created_by_user_id == user_id
    assert first_call_args.user_id is None  # First call should not have user_id

    # Get args for second call (user association)
    second_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[1][
            0
        ][0]
    )
    assert second_call_args.organization_id == organization_id
    assert second_call_args.call_id == call.id
    assert second_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert second_call_args.contact_id is None  # Second call should not have contact_id
    assert second_call_args.pipeline_id == pipeline_id
    assert second_call_args.account_id == account_id
    assert second_call_args.created_by_user_id == user_id
    assert second_call_args.user_id == user_id


async def test_handle_inbound_voice_call_without_contact(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    provider_client: AsyncMock,
    meeting_service: AsyncMock,
    voice_call_repository: AsyncMock,
    contact_query_service: AsyncMock,
    user_service: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test handling an inbound voice call without an existing contact."""
    # API call params
    call_sid = "TEST_CALL_SID"
    to_number = "+***********"  # Business number
    from_number = "+***********"  # Unknown caller
    caller = "TEST_CALLER"
    direction = CallDirection.INBOUND
    account_sid = "TEST_ACCOUNT_SID"

    organization_id = uuid4()
    provider_account_id = uuid4()
    org_phone_number_id = uuid4()
    user_id = uuid4()
    call_id = uuid4()
    account_id = uuid4()

    api_secret = "test_secret"  # noqa: S105
    encrypted_secret = fernet_encryption_manager.encrypt(api_secret)

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id=account_sid,
        api_key="API_KEY",
        api_secret=encrypted_secret,
        metadata={"application_sid": uuid4()},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock user for inbound call routing
    mock_user = Mock()
    mock_user.id = user_id

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=org_phone_number_id,
        voice_provider_account_id=provider_account_id,
        call_type=CallType.WEBRTC,
        caller_number=from_number,
        status=CallStatus.INITIATED,
        direction=direction,
        created_by_user_id=None,  # No contact exists yet
        caller_id=None,  # No contact exists yet
        contact_id=None,  # No contact exists yet
        pipeline_id=None,
        account_id=account_id,
        recipient_number=to_number,
        recipient_id=user_id,  # The user receiving the call
        metadata={"call_sid": call_sid, "provider": provider_account.provider},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Set up mock returns
    voice_provider_repository.get_provider_account_by_external_id.return_value = (
        provider_account
    )
    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.return_value = OrganizationPhoneNumberAssignment(
        organization_phone_number_id=org_phone_number_id,
        assigned_to=user_id,
    )
    contact_query_service.list_contact_ids_by_phone_number.return_value = (
        None  # No existing contact
    )
    voice_call_repository.insert.return_value = call
    user_service.get_by_id_and_organization_id.return_value = mock_user

    # Mock meeting dto
    mock_meeting = Mock()
    mock_meeting.id = uuid4()
    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting
    meeting_service.create_meeting.return_value = mock_meeting_dto

    mock_response = VoiceResponse()
    provider_client.get_actual_call_direction = Mock(return_value=direction)
    provider_client.build_call_response = Mock(return_value=mock_response)

    # Run method
    response = await service.handle_voice_call(
        call_sid=call_sid,
        to_number=to_number,
        from_number=from_number,
        caller=caller,
        direction=direction,
        account_sid=account_sid,
        account_id=account_id,
    )

    # Assertions
    assert isinstance(response, VoiceResponse)
    assert response == mock_response

    # Verify provider account lookup
    voice_provider_repository.get_provider_account_by_external_id.assert_called_once_with(
        external_id=account_sid
    )

    # Verify phone number lookup
    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.assert_called_once_with(
        organization_id=organization_id,
        phone_number=to_number,  # For inbound calls, we look up the destination number
    )

    # Verify contact lookup attempt
    contact_query_service.list_contact_ids_by_phone_number.assert_called_once_with(
        organization_id=organization_id,
        phone_number=from_number,
    )

    # Verify call direction check
    provider_client.get_actual_call_direction.assert_called_once_with(caller, direction)

    # Verify call response building
    provider_client.build_call_response.assert_called_once_with(
        call_sid=call_sid,
        from_number=from_number,
        to_number=to_number,
        user_id=user_id,
        display_caller_number=from_number,
        direction=direction,
        recipient_extension=None,
    )

    # Verify call creation
    voice_call_repository.insert.assert_called_once()
    inserted_call = voice_call_repository.insert.call_args[0][0]
    assert isinstance(inserted_call, Call)
    assert inserted_call.organization_id == organization_id
    assert inserted_call.contact_id is None
    assert inserted_call.external_id == call_sid
    assert inserted_call.direction == CallDirection.INBOUND
    assert inserted_call.recipient_id == user_id

    # Verify meeting creation
    meeting_service.create_meeting.assert_called_once()
    create_meeting_args = meeting_service.create_meeting.call_args[1]

    # Verify organization and user IDs
    assert create_meeting_args["organization_id"] == organization_id
    assert create_meeting_args["user_id"] == user_id
    assert create_meeting_args["request"].account_id == account_id
    assert create_meeting_args["request"].platform == MeetingProvider.VOICE
    assert (
        create_meeting_args["request"].reference_id_type
        == MeetingReferenceIdType.VOICE_V2
    )
    assert create_meeting_args["request"].reference_id == call_id
    assert create_meeting_args["request"].starts_at == call.created_at
    assert create_meeting_args["request"].started_at == call.created_at
    assert create_meeting_args["request"].title == "Call with +1 (647) 888-9999"

    # Verify meeting request

    meeting_request: CreateMeetingRequest = create_meeting_args["request"]
    assert meeting_request.platform == MeetingProvider.VOICE
    assert meeting_request.reference_id_type == MeetingReferenceIdType.VOICE_V2
    assert meeting_request.reference_id == call_id
    assert (
        meeting_request.title == "Call with +1 (647) 888-9999"
    )  # Updated title format

    # Verify invitees - should only have the user since there's no contact
    assert len(meeting_request.invitees) == 1
    organizer = meeting_request.invitees[0]
    assert organizer.user_id == user_id
    assert organizer.is_organizer is True


async def test_handle_voice_call_status(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    activity_service: AsyncMock,
    meeting_service: AsyncMock,
    quota_service: AsyncMock,
) -> None:
    # Initialize required variables
    from_number = "+**********"
    to_number = "+**********"
    call_sid = "TEST_CALL_SID"
    direction = CallDirection.OUTBOUND
    user_id = uuid4()
    contact_id = uuid4()
    pipeline_id = uuid4()
    metadata = {"some": "metadata"}
    now = zoned_utc_now()
    call_duration = 100

    # Mock call
    call = Call(
        id=uuid4(),
        organization_id=uuid4(),
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number=from_number,
        status=CallStatus.ANSWERED,
        direction=direction,
        created_by_user_id=user_id
        if direction == CallDirection.OUTBOUND
        else contact_id,
        caller_id=user_id if direction == CallDirection.OUTBOUND else contact_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        recipient_number=to_number,
        recipient_id=contact_id if direction == CallDirection.OUTBOUND else user_id,
        metadata=metadata,
        external_id=call_sid,
        created_at=now,
        updated_at=now,
    )

    voice_call_repository.find_by_external_id.return_value = call
    voice_call_repository.update_by_id.return_value = call

    # Mock meeting and activity
    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting.status = MeetingStatus.ACTIVE
    mock_meeting_dto.meeting.id = uuid4()
    mock_meeting_dto.meeting.title = "Test Meeting Title"
    mock_activity_uuid = uuid4()

    # Mock the activity that will be returned by
    # list_activities_by_reference_ids_and_type
    mock_activity = Mock()
    mock_activity.id = mock_activity_uuid
    activity_service.list_activities_by_reference_ids_and_type.return_value = [
        mock_activity
    ]

    meeting_service.end_active_meeting.return_value = (
        mock_meeting_dto,
        mock_activity_uuid,
    )
    meeting_service.get_meeting_by_reference_id.return_value = mock_meeting_dto

    # run method
    test_timestamp = TEST_DATETIME
    await service.handle_voice_call_status(
        call_sid="TEST_CALL_SID",
        parent_call_sid="TEST_PARENT_CALL_SID",
        call_status=CallStatus.ANSWERED,
        sequence_number=1,
        timestamp=test_timestamp,
        call_duration=call_duration,
        account_sid="TEST_ACCOUNT_SID",
    )

    # assertions
    user_id = not_none(
        call.caller_id
        if call.direction == CallDirection.OUTBOUND
        else call.recipient_id
    )

    activity_service.patch_activity_by_id.assert_called_once_with(
        activity_id=mock_activity_uuid,
        organization_id=call.organization_id,
        req=ActivityPatchRequest(
            sub_type=ActivitySubType.CALL_OUTBOUND_ANSWERED,
            display_name=mock_meeting_dto.meeting.title,
        ),
    )

    meeting_service.patch_meeting_v2.assert_called_once_with(
        meeting_id=mock_meeting_dto.meeting.id,
        organization_id=call.organization_id,
        request=PatchMeetingRequest(
            title=mock_meeting_dto.meeting.title,
        ),
        user_id=user_id,
    )

    # Verify quota is correctly recorded
    quota_service.increase_usage.assert_called_once()
    usage_call_args = quota_service.increase_usage.call_args[1]
    assert usage_call_args["organization_id"] == call.organization_id
    assert usage_call_args["entity_id"] == not_none(call.created_by_user_id)
    assert usage_call_args["entity_type"] == QuotaConsumerEntityType.USER
    assert usage_call_args["resource"] == QuotaConsumingResource.VOICE_SECONDS
    assert usage_call_args["usage"] == call_duration
    assert "timestamp" in usage_call_args  # Verify timestamp is passed

    mock_activity = Mock()
    mock_activity.id = mock_activity_uuid
    mock_activity.sub_type = ActivitySubType.CALL_INBOUND_ANSWERED
    activity_service.list_activities_by_reference_ids_and_type.return_value = [
        mock_activity
    ]

    # run again to check that the activity is not updated again
    activity_service.patch_activity_by_id.reset_mock()
    quota_service.increase_usage.reset_mock()

    await service.handle_voice_call_status(
        call_sid="TEST_CALL_SID",
        parent_call_sid="TEST_PARENT_CALL_SID",
        call_status=CallStatus.ANSWERED,
        sequence_number=1,
        timestamp=TEST_DATETIME,
        call_duration=call_duration,
        account_sid="TEST_ACCOUNT_SID",
    )

    activity_service.patch_activity_by_id.assert_not_called()

    # Verify quota is recorded again even for the same call
    quota_service.increase_usage.assert_called_once()


async def test_handle_outbound_voice_call_without_contact(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    provider_client: AsyncMock,
) -> None:
    """Test handling an outbound voice call without a contact ID raises an error."""
    # API call params
    call_sid = "TEST_CALL_SID"
    to_number = "+***********"  # Number to call
    from_number = "+***********"  # Business number
    caller = "TEST_CALLER"
    direction = CallDirection.OUTBOUND
    account_sid = "TEST_ACCOUNT_SID"

    organization_id = uuid4()
    provider_account_id = uuid4()
    org_phone_number_id = uuid4()
    user_id = uuid4()

    api_secret = "test_secret"  # noqa: S105
    encrypted_secret = fernet_encryption_manager.encrypt(api_secret)

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id=account_sid,
        api_key="API_KEY",
        api_secret=encrypted_secret,
        metadata={"application_sid": uuid4()},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Set up mock returns
    voice_provider_repository.get_provider_account_by_external_id.return_value = (
        provider_account
    )
    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.return_value = OrganizationPhoneNumberAssignment(
        organization_phone_number_id=org_phone_number_id,
        assigned_to=user_id,
    )
    provider_client.get_actual_call_direction = Mock(return_value=direction)

    # Run method and expect error
    with pytest.raises(ValueError) as exc_info:
        await service.handle_voice_call(
            call_sid=call_sid,
            to_number=to_number,
            from_number=from_number,
            caller=caller,
            direction=direction,
            account_sid=account_sid,
        )

    assert str(exc_info.value) == "contact_id is required for outbound calls"

    # Verify provider account lookup
    voice_provider_repository.get_provider_account_by_external_id.assert_called_once_with(
        external_id=account_sid
    )

    # Verify phone number lookup
    voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number.assert_called_once_with(
        organization_id=organization_id,
        phone_number=from_number,  # For outbound calls, we look up the source number
    )

    # Verify call direction check
    provider_client.get_actual_call_direction.assert_called_once_with(caller, direction)


async def test_handle_call_redirect(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    voice_provider_repository: AsyncMock,
    user_service: AsyncMock,
    provider_client: AsyncMock,
    user_preference_service: AsyncMock,
    voicemail_greeting_s3_manager: AsyncMock,
) -> None:
    """Test handling call redirect with different call statuses."""
    # Initialize test data
    call_sid = "TEST_CALL_SID"
    parent_call_sid = "TEST_PARENT_CALL_SID"
    account_sid = "TEST_ACCOUNT_SID"
    organization_id = uuid4()
    user_id = uuid4()
    from_number = "+***********"

    # Mock call object
    call = Call(
        id=uuid4(),
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number=from_number,
        status=CallStatus.RINGING,
        direction=CallDirection.INBOUND,
        created_by_user_id=None,
        caller_id=None,
        contact_id=None,
        pipeline_id=None,
        recipient_number="+***********",
        recipient_id=user_id,
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=uuid4(),
        organization_id=organization_id,
        provider="mockProvider",
        external_id=account_sid,
        api_key="API_KEY",
        api_secret="encrypted_secret",  # noqa: S106
        metadata={"application_sid": uuid4()},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock user
    mock_user = Mock()
    mock_user.id = user_id

    # Mock voicemail greeting URL
    mock_greeting_url = "https://example.com/greeting.mp3"
    mock_greeting_s3_key = "voicemail/greeting.mp3"

    # Mock voice preferences - Fix the structure to match expected dictionary
    mock_voice_preferences = UserPreferenceResponse(
        voice=UserPreferenceDomainVoice(
            voicemail=VoiceMailPreferences(
                active_greeting_recording_s3_key=mock_greeting_s3_key,
                inactive_greeting_recording_s3_key=None,
            ),
            call_forwarding=CallForwardingPreferences(
                enabled=True,
            ),
            followup_email=None,
        )
    )

    # Set up mock returns
    voice_call_repository.find_by_external_id.return_value = call
    voice_provider_repository.get_provider_account_by_external_id.return_value = (
        provider_account
    )
    user_service.get_by_id_and_organization_id.return_value = mock_user
    user_preference_service.get_user_preference.return_value = mock_voice_preferences
    voicemail_greeting_s3_manager.generate_presigned_url.return_value = (
        mock_greeting_url,
        None,
    )

    # Mock provider client responses
    mock_redirect_response = VoiceResponse()
    mock_hangup_response = VoiceResponse()
    provider_client.build_redirect_response = Mock(return_value=mock_redirect_response)
    provider_client.hangup_response = Mock(return_value=mock_hangup_response)

    # Test cases for different call statuses
    redirect_statuses = [
        CallStatus.RINGING,
        CallStatus.NO_ANSWER,
        CallStatus.BUSY,
        CallStatus.FAILED,
    ]
    hangup_statuses = [CallStatus.INITIATED, CallStatus.IN_PROGRESS]

    # Test redirect cases
    for status in redirect_statuses:
        response = await service.handle_call_redirect(
            account_sid=account_sid,
            call_sid=call_sid,
            parent_call_sid=parent_call_sid,
            call_status=status,
        )
        assert response == mock_redirect_response
        provider_client.build_redirect_response.assert_called_with(
            from_number=from_number,
            user=mock_user,
            call_sid=call_sid,
            voicemail_greeting_url=mock_greeting_url,
        )

    # Test hangup cases
    for status in hangup_statuses:
        response = await service.handle_call_redirect(
            account_sid=account_sid,
            call_sid=call_sid,
            parent_call_sid=parent_call_sid,
            call_status=status,
        )
        assert response == mock_hangup_response
        provider_client.hangup_response.assert_called()

    # Verify repository calls
    voice_call_repository.find_by_external_id.assert_called()
    voice_provider_repository.get_provider_account_by_external_id.assert_called_with(
        external_id=account_sid
    )
    user_service.get_by_id_and_organization_id.assert_called_with(
        user_id=user_id,
        organization_id=organization_id,
    )
    user_preference_service.get_user_preference.assert_called_with(
        user_id=user_id, organization_id=organization_id, key=UserPreferenceKeys.VOICE
    )
    voicemail_greeting_s3_manager.generate_presigned_url.assert_called_with(
        key=mock_greeting_s3_key,
    )


async def test_handle_answering_machine_detection(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    activity_service: AsyncMock,
    meeting_service: AsyncMock,
) -> None:
    """Test handling answering machine detection."""
    call_sid = "TEST_CALL_SID"
    parent_call_sid = "TEST_PARENT_CALL_SID"
    account_sid = "TEST_ACCOUNT_SID"
    answered_by = "machine"
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()

    call = Call(
        id=uuid4(),
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+***********",
        status=CallStatus.IN_PROGRESS,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        pipeline_id=None,
        recipient_number="+***********",
        recipient_id=contact_id,
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    mock_meeting = Mock()
    mock_meeting.meeting.id = uuid4()
    mock_activity = Mock()
    mock_activity.id = uuid4()

    voice_call_repository.find_by_external_id.return_value = call
    meeting_service.get_meeting_by_reference_id.return_value = mock_meeting
    activity_service.list_activities_by_reference_ids_and_type.return_value = [
        mock_activity
    ]

    await service.handle_answering_machine_detection(
        call_sid=call_sid,
        account_sid=account_sid,
        answered_by=answered_by,
        parent_call_sid=parent_call_sid,
    )


async def test_update_call_recording(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    voice_provider_repository: AsyncMock,
    provider_client: AsyncMock,
) -> None:
    """Test updating call recording status."""
    # Test data
    call_sid = "TEST_CALL_SID"
    organization_id = uuid4()
    provider_account_id = uuid4()
    call_id = uuid4()

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=provider_account_id,
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.IN_PROGRESS,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=uuid4(),
        caller_id=uuid4(),
        recipient_number="+**********",
        recipient_id=uuid4(),
        metadata={"call_sid": call_sid},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id="TEST_ACCOUNT_SID",
        api_key="API_KEY",
        api_secret="encrypted_secret",  # noqa: S106
        metadata={},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Setup mocks
    voice_call_repository.find_by_external_id.return_value = call
    voice_provider_repository.get_provider_account.return_value = provider_account
    provider_client.update_call_recording.return_value = None

    # Test execution
    request = UpdateCallRecordingRequest(
        status=UpdateCallRecordingStatus.PAUSED, external_call_id=call_sid
    )
    await service.update_call_recording(request=request)

    # Verify mocks were called correctly
    voice_call_repository.find_by_external_id.assert_called_once_with(call_sid)
    voice_provider_repository.get_provider_account.assert_called_once_with(
        organization_id=organization_id,
        provider_account_id=provider_account_id,
    )
    provider_client.update_call_recording.assert_called_once_with(
        call_sid=call_sid,
        status=UpdateCallRecordingStatus.PAUSED,
        provider_account_external_id=provider_account.external_id,
    )
    voice_call_repository.update_by_external_id.assert_called_once()


async def test_handle_voice_call_status_with_notifications(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    activity_service: AsyncMock,
    meeting_service: AsyncMock,
    notification_service: AsyncMock,
) -> None:
    """Test handling voice call status updates with notifications."""
    # Enable notifications in settings
    settings.voice_enable_notifications = True

    # Mock the _trigger_follow_up_email method to prevent unawaited coroutines
    service._trigger_follow_up_email = AsyncMock()  # type: ignore

    # Initialize test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    call_id = uuid4()
    meeting_id = uuid4()
    activity_id = uuid4()
    call_sid = "TEST_CALL_SID"
    call_duration = 30  # Add call duration for calculating started_at

    # Mock call object for inbound missed call
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.IN_PROGRESS,
        direction=CallDirection.INBOUND,
        created_by_user_id=user_id,  # Phone number owner
        caller_id=contact_id,  # Caller
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=user_id,  # Recipient
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock meeting with participants
    mock_meeting = Mock()
    mock_meeting.id = meeting_id
    mock_meeting.status = MeetingStatus.ACTIVE
    mock_meeting.title = "Call with Test Contact (+**********)"

    # Create participant mocks with string names
    contact_participant = Mock()
    contact_participant.contact_id = contact_id
    contact_participant.user_id = None
    contact_participant.name = "Test Contact"  # String instead of Mock

    user_participant = Mock()
    user_participant.contact_id = None
    user_participant.user_id = user_id
    user_participant.name = "Test User"  # String instead of Mock

    mock_meeting.meeting_participants = [
        contact_participant,
        user_participant,
    ]

    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting

    # Mock activity
    mock_activity = Mock()
    mock_activity.id = activity_id

    # Set up mock returns
    voice_call_repository.find_by_external_id.return_value = call
    voice_call_repository.update_by_id.return_value = call
    meeting_service.get_meeting_by_reference_id.return_value = mock_meeting_dto
    meeting_service.end_active_meeting.return_value = (mock_meeting_dto, mock_activity)
    activity_service.list_activities_by_reference_ids_and_type.return_value = [
        mock_activity
    ]

    # Important: Mock patch_meeting_v2 to return the updated meeting
    meeting_service.patch_meeting_v2.return_value = mock_meeting

    # Test cases for notification-triggering statuses
    notification_test_cases: list[
        tuple[
            CallStatus,
            Literal[
                NotificationWorkflow.VOICE_CALL_MISSED,
                NotificationWorkflow.VOICE_CALL_VOICEMAIL_LEFT,
            ],
        ]
    ] = [
        (CallStatus.NO_ANSWER, NotificationWorkflow.VOICE_CALL_MISSED),
        (CallStatus.BUSY, NotificationWorkflow.VOICE_CALL_MISSED),
        (CallStatus.VOICEMAIL_LEFT, NotificationWorkflow.VOICE_CALL_VOICEMAIL_LEFT),
    ]

    for call_status, notification_type in notification_test_cases:
        # Reset mocks
        notification_service.send_notification.reset_mock()
        meeting_service.end_active_meeting.reset_mock()
        activity_service.patch_activity_by_id.reset_mock()
        meeting_service.patch_meeting_v2.reset_mock()
        meeting_service.get_meeting_by_reference_id.reset_mock()
        voice_call_repository.update_by_id.reset_mock()

        # Reset return values after mock reset
        meeting_service.end_active_meeting.return_value = (
            mock_meeting_dto,
            mock_activity,
        )
        meeting_service.patch_meeting_v2.return_value = mock_meeting
        meeting_service.get_meeting_by_reference_id.return_value = mock_meeting_dto

        # Important: Create a new call object with updated status
        updated_call = Call(
            id=call.id,
            organization_id=organization_id,
            organization_phone_number_id=call.organization_phone_number_id,
            voice_provider_account_id=call.voice_provider_account_id,
            call_type=call.call_type,
            caller_number=call.caller_number,
            status=call_status,  # Updated status
            direction=call.direction,
            created_by_user_id=call.created_by_user_id,
            caller_id=call.caller_id,
            contact_id=call.contact_id,
            recipient_number=call.recipient_number,
            recipient_id=call.recipient_id,
            metadata=call.metadata,
            external_id=call.external_id,
            created_at=call.created_at,
            updated_at=call.updated_at,
        )
        voice_call_repository.update_by_id.return_value = updated_call
        voice_call_repository.find_by_external_id.return_value = updated_call

        # Update call status
        await service.handle_voice_call_status(
            call_sid=call_sid,
            call_status=call_status,
            parent_call_sid=None,
            sequence_number=1,
            timestamp=TEST_DATETIME,
            call_duration=call_duration,  # Include call_duration to ensure started_at is calculated
        )

        # Construct expected notification data with explicit type
        expected_notification_data = NotificationVoiceCallData(
            type=notification_type,
            call_id=str(call.id),
            call_status=call_status,
            meeting_page=f"/meetings/{meeting_id}",
            caller_number=call.caller_number,
            contact_id=str(contact_id),
            contact_name="Test Contact",
            user_id=str(user_id),
            user_name="Test User",
        )

        expected_notification_request = SendNotificationRequest(
            actor_user_id=user_id,
            recipient_user_ids=[user_id],
            reference_id=str(meeting_id),
            reference_id_type=NotificationReferenceIdType.MEETING,
            activity_id=activity_id,
            idempotency_key=f"voice_call_{call.id}_{call.updated_at}",
            data=expected_notification_data,
        )

        # Verify the flow in order
        meeting_service.get_meeting_by_reference_id.assert_awaited()
        meeting_service.get_meeting_by_reference_id.assert_awaited_once()
        meeting_service.end_active_meeting.assert_awaited_once()
        activity_service.patch_activity_by_id.assert_awaited_once()
        meeting_service.patch_meeting_v2.assert_awaited_once()

        notification_service.send_notification.assert_awaited_once_with(
            send_notification_request=expected_notification_request,
            organization_id=organization_id,
        )


async def test_handle_recording_completed_without_notification(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    voice_provider_repository: AsyncMock,
    provider_client: AsyncMock,
    call_recording_s3_manager: AsyncMock,
    meeting_service: AsyncMock,
    activity_service: AsyncMock,
    notification_service: AsyncMock,
    ff_service: AsyncMock,
) -> None:
    """Test handling recording completed without sending notification."""
    # Disable notifications in settings
    settings.voice_enable_notifications = False

    # Also disable feature flag
    ff_service.is_enabled.return_value = False

    # Test data
    call_sid = "TEST_CALL_SID"
    recording_sid = "TEST_RECORDING_SID"
    recording_url = "https://example.com/recording.mp3"
    account_sid = "TEST_ACCOUNT_SID"
    recording_duration = 30  # seconds
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    call_id = uuid4()
    meeting_id = uuid4()
    activity_id = uuid4()

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=uuid4(),
        organization_id=organization_id,
        provider="mockProvider",
        external_id=account_sid,
        api_key="API_KEY",
        api_secret="encrypted_secret",  # noqa: S106
        metadata={"application_sid": uuid4()},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock call object for inbound call with voicemail
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=provider_account.id,
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.NO_ANSWER,
        direction=CallDirection.INBOUND,
        created_by_user_id=user_id,
        caller_id=contact_id,
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=user_id,
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock meeting
    mock_meeting = Mock()
    mock_meeting.id = meeting_id
    mock_meeting.status = MeetingStatus.ACTIVE
    mock_meeting.title = "Call with Test Contact (+**********)"

    # Create participant mocks
    contact_participant = Mock()
    contact_participant.contact_id = contact_id
    contact_participant.user_id = None
    contact_participant.name = "Test Contact"

    user_participant = Mock()
    user_participant.contact_id = None
    user_participant.user_id = user_id
    user_participant.name = "Test User"

    mock_meeting.meeting_participants = [
        contact_participant,
        user_participant,
    ]

    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting

    # Mock activity
    mock_activity = Mock()
    mock_activity.id = activity_id

    service._trigger_follow_up_email = AsyncMock()  # type: ignore

    # Set up mock returns
    voice_provider_repository.get_provider_account_by_external_id.return_value = (
        provider_account
    )
    voice_call_repository.find_by_external_id.return_value = call
    voice_call_repository.update_by_external_id.return_value = call
    provider_client.download_recording.return_value = b"fake audio data"

    # Important: Make is_voicemail_left a regular Mock, not AsyncMock
    provider_client.is_voicemail_left = Mock(return_value=True)

    call_recording_s3_manager.generate_presigned_url.return_value = (
        "https://presigned-url.com",
        3600,
    )
    meeting_service.get_meeting_by_reference_id.return_value = mock_meeting_dto
    activity_service.list_activities_by_reference_ids_and_type.return_value = [
        mock_activity
    ]
    meeting_service.patch_meeting_v2.return_value = mock_meeting

    # Execute test
    await service.handle_recording_completed(
        call_sid=call_sid,
        recording_sid=recording_sid,
        recording_url=recording_url,
        account_sid=account_sid,
        recording_duration=recording_duration,
    )

    # Verify provider account lookup
    voice_provider_repository.get_provider_account_by_external_id.assert_awaited_once_with(
        external_id=account_sid
    )

    # Verify call lookup
    voice_call_repository.find_by_external_id.assert_awaited_once_with(call_sid)

    # Verify recording download
    provider_client.download_recording.assert_awaited_once_with(
        account_sid=account_sid,
        recording_sid=recording_sid,
    )

    # Verify S3 upload and URL generation
    call_recording_s3_manager.copy_chunks_to_s3.assert_awaited_once()
    call_recording_s3_manager.generate_presigned_url.assert_awaited_once()

    # Verify voicemail detection - use regular assert for the Mock
    provider_client.is_voicemail_left.assert_called_once()

    # Verify meeting lookup and update
    meeting_service.get_meeting_by_reference_id.assert_awaited_once_with(
        reference_id=call.id,
        reference_id_type=MeetingReferenceIdType.VOICE_V2,
        organization_id=organization_id,
    )

    # Verify activity lookup
    activity_service.list_activities_by_reference_ids_and_type.assert_awaited_once()

    # Verify meeting title update with started_at field
    meeting_service.patch_meeting_v2.assert_awaited_once()
    patch_args = meeting_service.patch_meeting_v2.call_args[1]
    assert patch_args["meeting_id"] == mock_meeting.id
    assert patch_args["organization_id"] == organization_id
    assert "request" in patch_args
    assert isinstance(patch_args["request"], PatchMeetingRequest)

    # Verify feature flag check
    ff_service.is_enabled.assert_awaited_once()

    # Verify notification was NOT sent
    notification_service.send_notification.assert_not_awaited()

    # Verify call update (without notification ID)
    voice_call_repository.update_by_external_id.assert_awaited_once()
    update_call_args = voice_call_repository.update_by_external_id.call_args[0]
    assert update_call_args[0] == call_sid

    # Verify the metadata doesn't contain notification ID
    update_dict = voice_call_repository.update_by_external_id.call_args[1][
        "column_to_update"
    ]
    assert "metadata" in update_dict
    assert "latest_notification_id" not in update_dict["metadata"]

    # Verify meeting analysis was triggered
    meeting_service.analyze_ended_meeting.assert_awaited_once_with(
        reference_id=call.id,
        reference_id_type=MeetingReferenceIdType.VOICE_V2,
        organization_id=organization_id,
        media_url="https://presigned-url.com",
    )


async def test_initiate_bridge_call_with_extension(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    voice_call_repository: AsyncMock,
    user_service: AsyncMock,
    provider_client: AsyncMock,
    meeting_service: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test initiating a bridge call successfully."""
    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    provider_account_id = uuid4()
    to_number = "+***********"
    to_extension = "1234"
    call_id = uuid4()
    meeting_id = uuid4()
    external_call_id = "TEST_EXTERNAL_CALL_ID"
    pipeline_id = uuid4()
    account_id = uuid4()

    # Mock request
    request = InitiateCallRequest(
        to_number=to_number,
        provider=VoiceProvider.TWILIO,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        type=CallType.BRIDGE,
        recipient_extension=to_extension,
    )

    # Mock user
    mock_user = Mock()
    mock_user.id = user_id
    mock_user.phone_number = "+***********"
    user_service.get_by_id_and_organization_id.return_value = mock_user

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id="TEST_ACCOUNT_SID",
        api_key="API_KEY",
        api_secret="encrypted_secret",  # noqa: S106
        metadata={},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )
    voice_provider_repository.get_provider_account_by_organization_and_provider.return_value = provider_account

    # Mock phone number
    reevo_phone_number = {
        "organization_phone_number_id": uuid4(),
        "phone_number_id": uuid4(),
        "number": "+***********",
        "assigned_to": user_id,
    }
    voice_phone_number_repository.get_existing_number_owned_by_user.return_value = (
        reevo_phone_number
    )

    # Mock call
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=UUID(
            str(reevo_phone_number["organization_phone_number_id"])
        ),
        voice_provider_account_id=provider_account_id,
        call_type=CallType.BRIDGE,
        caller_number=mock_user.phone_number,
        status=CallStatus.INITIATED,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        pipeline_id=None,
        account_id=None,
        recipient_number=to_number,
        recipient_id=contact_id,
        metadata={
            "bridge_number": str(reevo_phone_number["number"]),
            "bridge_number_id": str(reevo_phone_number["phone_number_id"]),
        },
        external_id=None,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
        recipient_extension=to_extension,
    )
    voice_call_repository.insert.return_value = call

    # Mock meeting
    mock_meeting = Mock()
    mock_meeting.id = meeting_id
    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting
    meeting_service.create_meeting.return_value = mock_meeting_dto

    # Mock external call object
    mock_external_call = Mock()
    mock_external_call.external_id = external_call_id
    provider_client.create_bridge_call.return_value = mock_external_call

    # Execute test
    response = await service.initiate_bridge_call(
        organization_id=organization_id,
        user_id=user_id,
        request=request,
    )

    # Verify result
    assert response.call_id == call_id
    assert response.meeting_id == meeting_id
    assert response.external_call_id == external_call_id

    # Verify user lookup
    user_service.get_by_id_and_organization_id.assert_called_once_with(
        user_id=user_id,
        organization_id=organization_id,
    )

    # Verify provider account lookup
    voice_provider_repository.get_provider_account_by_organization_and_provider.assert_called_once_with(
        organization_id=organization_id,
        provider=VoiceProvider.TWILIO,
    )

    # Verify phone number lookup
    voice_phone_number_repository.get_existing_number_owned_by_user.assert_called_once_with(
        organization_id=organization_id,
        user_id=user_id,
    )

    # Verify call creation
    voice_call_repository.insert.assert_called_once()
    inserted_call = voice_call_repository.insert.call_args[0][0]
    assert isinstance(inserted_call, Call)
    assert inserted_call.organization_id == organization_id
    assert inserted_call.contact_id == contact_id
    assert inserted_call.direction == CallDirection.OUTBOUND
    assert inserted_call.call_type == CallType.BRIDGE

    # Verify meeting creation
    meeting_service.create_meeting.assert_called_once()

    # Verify bridge call creation
    provider_client.create_bridge_call.assert_called_once_with(
        from_number=str(reevo_phone_number["number"]),
        caller_number=mock_user.phone_number,
        display_caller_number=mock_user.phone_number,  # default behavior
        to_number=to_number,
        external_id=provider_account.external_id,
        call_id=call_id,
        recipient_extension=to_extension,
    )

    # Verify call update with external ID
    voice_call_repository.update_by_id.assert_called_once_with(
        call_id,
        column_to_update={"external_id": external_call_id},
    )

    # Verify domain_crm_association creation
    assert domain_crm_association_service.create_domain_crm_association.call_count == 2

    # Get args for first call (contact association)
    first_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[0][
            0
        ][0]
    )
    assert first_call_args.organization_id == organization_id
    assert first_call_args.call_id == call.id
    assert first_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert first_call_args.contact_id == contact_id
    assert first_call_args.pipeline_id == pipeline_id
    assert first_call_args.account_id == account_id
    assert first_call_args.created_by_user_id == user_id
    assert first_call_args.user_id is None  # First call should not have user_id

    # Get args for second call (user association)
    second_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[1][
            0
        ][0]
    )
    assert second_call_args.organization_id == organization_id
    assert second_call_args.call_id == call.id
    assert second_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert second_call_args.contact_id is None  # Second call should not have contact_id
    assert second_call_args.pipeline_id == pipeline_id
    assert second_call_args.account_id == account_id
    assert second_call_args.created_by_user_id == user_id
    assert second_call_args.user_id == user_id


async def test_initiate_bridge_call_success(
    service: VoiceCallService,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
    voice_call_repository: AsyncMock,
    user_service: AsyncMock,
    provider_client: AsyncMock,
    meeting_service: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test initiating a bridge call successfully."""
    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    provider_account_id = uuid4()
    to_number = "+***********"
    call_id = uuid4()
    meeting_id = uuid4()
    external_call_id = "TEST_EXTERNAL_CALL_ID"
    pipeline_id = uuid4()
    account_id = uuid4()

    # Mock request
    request = InitiateCallRequest(
        to_number=to_number,
        provider=VoiceProvider.TWILIO,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        type=CallType.BRIDGE,
    )

    # Mock user
    mock_user = Mock()
    mock_user.id = user_id
    mock_user.phone_number = "+***********"
    user_service.get_by_id_and_organization_id.return_value = mock_user

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id="TEST_ACCOUNT_SID",
        api_key="API_KEY",
        api_secret="encrypted_secret",  # noqa: S106
        metadata={},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )
    voice_provider_repository.get_provider_account_by_organization_and_provider.return_value = provider_account

    # Mock phone number
    reevo_phone_number = {
        "organization_phone_number_id": uuid4(),
        "phone_number_id": uuid4(),
        "number": "+***********",
        "assigned_to": user_id,
    }
    voice_phone_number_repository.get_existing_number_owned_by_user.return_value = (
        reevo_phone_number
    )

    # Mock call
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=UUID(
            str(reevo_phone_number["organization_phone_number_id"])
        ),
        voice_provider_account_id=provider_account_id,
        call_type=CallType.BRIDGE,
        caller_number=mock_user.phone_number,
        status=CallStatus.INITIATED,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        pipeline_id=None,
        account_id=None,
        recipient_number=to_number,
        recipient_id=contact_id,
        metadata={
            "bridge_number": str(reevo_phone_number["number"]),
            "bridge_number_id": str(reevo_phone_number["phone_number_id"]),
        },
        external_id=None,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    domain_crm_association_contact = DomainCRMAssociation(
        id=uuid4(),
        organization_id=organization_id,
        domain_type=DomainType.VOICE_CALL,
        call_id=call_id,
        created_by_user_id=user_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        created_at=TEST_DATETIME,
    )

    domain_crm_association_user = DomainCRMAssociation(
        id=uuid4(),
        organization_id=organization_id,
        domain_type=DomainType.VOICE_CALL,
        call_id=call_id,
        user_id=user_id,
        created_by_user_id=user_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        created_at=TEST_DATETIME,
    )

    domain_crm_association_service.create_domain_crm_association.side_effect = [
        domain_crm_association_contact,
        domain_crm_association_user,
    ]

    voice_call_repository.insert.return_value = call

    # Mock meeting
    mock_meeting = Mock()
    mock_meeting.id = meeting_id
    mock_meeting_dto = Mock()
    mock_meeting_dto.meeting = mock_meeting
    meeting_service.create_meeting.return_value = mock_meeting_dto

    # Mock external call object
    mock_external_call = Mock()
    mock_external_call.external_id = external_call_id
    provider_client.create_bridge_call.return_value = mock_external_call

    # Execute test
    response = await service.initiate_bridge_call(
        organization_id=organization_id,
        user_id=user_id,
        request=request,
    )

    # Verify result
    assert response.call_id == call_id
    assert response.meeting_id == meeting_id
    assert response.external_call_id == external_call_id

    # Verify user lookup
    user_service.get_by_id_and_organization_id.assert_called_once_with(
        user_id=user_id,
        organization_id=organization_id,
    )

    # Verify provider account lookup
    voice_provider_repository.get_provider_account_by_organization_and_provider.assert_called_once_with(
        organization_id=organization_id,
        provider=VoiceProvider.TWILIO,
    )

    # Verify phone number lookup
    voice_phone_number_repository.get_existing_number_owned_by_user.assert_called_once_with(
        organization_id=organization_id,
        user_id=user_id,
    )

    # Verify call creation
    voice_call_repository.insert.assert_called_once()
    inserted_call = voice_call_repository.insert.call_args[0][0]
    assert isinstance(inserted_call, Call)
    assert inserted_call.organization_id == organization_id
    assert inserted_call.contact_id == contact_id
    assert inserted_call.direction == CallDirection.OUTBOUND
    assert inserted_call.call_type == CallType.BRIDGE

    # Verify meeting creation
    meeting_service.create_meeting.assert_called_once()

    # Verify bridge call creation
    provider_client.create_bridge_call.assert_called_once_with(
        from_number=str(reevo_phone_number["number"]),
        caller_number=mock_user.phone_number,
        display_caller_number=mock_user.phone_number,  # default behavior
        to_number=to_number,
        external_id=provider_account.external_id,
        call_id=call_id,
        recipient_extension=None,
    )

    # Verify call update with external ID
    voice_call_repository.update_by_id.assert_called_once_with(
        call_id,
        column_to_update={"external_id": external_call_id},
    )

    # Verify domain_crm_association creation
    assert domain_crm_association_service.create_domain_crm_association.call_count == 2

    # Get args for first call (contact association)
    first_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[0][
            0
        ][0]
    )
    assert first_call_args.organization_id == organization_id
    assert first_call_args.call_id == call.id
    assert first_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert first_call_args.contact_id == contact_id
    assert first_call_args.pipeline_id == pipeline_id
    assert first_call_args.account_id == account_id
    assert first_call_args.created_by_user_id == user_id
    assert first_call_args.user_id is None  # First call should not have user_id

    # Get args for second call (user association)
    second_call_args = (
        domain_crm_association_service.create_domain_crm_association.call_args_list[1][
            0
        ][0]
    )
    assert second_call_args.organization_id == organization_id
    assert second_call_args.call_id == call.id
    assert second_call_args.meeting_id == mock_meeting_dto.meeting.id
    assert second_call_args.contact_id is None  # Second call should not have contact_id
    assert second_call_args.pipeline_id == pipeline_id
    assert second_call_args.account_id == account_id
    assert second_call_args.created_by_user_id == user_id
    assert second_call_args.user_id == user_id


async def test_initiate_bridge_call_user_not_found(
    service: VoiceCallService,
    user_service: AsyncMock,
) -> None:
    """Test initiating a bridge call when user is not found."""
    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    to_number = "+***********"

    # Mock request
    request = InitiateCallRequest(
        to_number=to_number,
        provider=VoiceProvider.TWILIO,
        contact_id=uuid4(),
        type=CallType.BRIDGE,
    )

    # Mock user not found
    user_service.get_by_id_and_organization_id.return_value = None

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.initiate_bridge_call(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )
    assert str(exc_info.value) == "User phone number not found"

    # Verify user lookup
    user_service.get_by_id_and_organization_id.assert_called_once_with(
        user_id=user_id,
        organization_id=organization_id,
    )


async def test_initiate_bridge_call_no_phone_number(
    service: VoiceCallService,
    user_service: AsyncMock,
) -> None:
    """Test initiating a bridge call when user has no phone number."""
    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    to_number = "+***********"

    # Mock request
    request = InitiateCallRequest(
        to_number=to_number,
        provider=VoiceProvider.TWILIO,
        contact_id=uuid4(),
        type=CallType.BRIDGE,
    )

    # Mock user with no phone number
    mock_user = Mock()
    mock_user.id = user_id
    mock_user.phone_number = None
    user_service.get_by_id_and_organization_id.return_value = mock_user

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.initiate_bridge_call(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )
    assert str(exc_info.value) == "User phone number not found"

    # Verify user lookup
    user_service.get_by_id_and_organization_id.assert_called_once_with(
        user_id=user_id,
        organization_id=organization_id,
    )


async def test_initiate_bridge_call_no_reevo_number(
    service: VoiceCallService,
    user_service: AsyncMock,
    voice_provider_repository: AsyncMock,
    voice_phone_number_repository: AsyncMock,
) -> None:
    """Test initiating a bridge call when no Reevo phone number is found."""
    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    provider_account_id = uuid4()
    to_number = "+***********"

    # Mock request
    request = InitiateCallRequest(
        to_number=to_number,
        provider=VoiceProvider.TWILIO,
        contact_id=uuid4(),
        type=CallType.BRIDGE,
    )

    # Mock user
    mock_user = Mock()
    mock_user.id = user_id
    mock_user.phone_number = "+***********"
    user_service.get_by_id_and_organization_id.return_value = mock_user

    # Mock provider account
    provider_account = VoiceProviderAccount(
        id=provider_account_id,
        organization_id=organization_id,
        provider="mockProvider",
        external_id="TEST_ACCOUNT_SID",
        api_key="API_KEY",
        api_secret="encrypted_secret",  # noqa: S106
        metadata={},
        created_by_user_id=uuid4(),
        status=VoiceProviderAccountStatus.ACTIVE,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )
    voice_provider_repository.get_provider_account.return_value = provider_account

    # Mock no phone number found
    voice_phone_number_repository.get_existing_number_owned_by_user.return_value = None

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.initiate_bridge_call(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )
    assert str(exc_info.value) == "Reevo phone number not found"

    # Verify user lookup
    user_service.get_by_id_and_organization_id.assert_called_once_with(
        user_id=user_id,
        organization_id=organization_id,
    )

    # Verify provider account lookup
    voice_provider_repository.get_provider_account_by_organization_and_provider.assert_called_once_with(
        organization_id=organization_id,
        provider=VoiceProvider.TWILIO,
    )

    # Verify phone number lookup
    voice_phone_number_repository.get_existing_number_owned_by_user.assert_called_once_with(
        organization_id=organization_id,
        user_id=user_id,
    )


async def test_trigger_follow_up_email_success(
    service: VoiceCallService,
    user_service: AsyncMock,
    contact_query_service: AsyncMock,
    email_account_service: AsyncMock,
    ff_service: AsyncMock,
    user_preference_service: AsyncMock,
    organization_preference_service: AsyncMock,
) -> None:
    """Test triggering a follow-up email after a missed call."""
    # Enable feature flag
    ff_service.is_enabled.return_value = True

    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    account_id = uuid4()
    pipeline_id = uuid4()
    call_id = uuid4()
    user_template_id = UUID("0093fa34-7c76-455d-bef8-1884336c58bc")
    org_template_id = UUID("1193fa34-7c76-455d-bef8-1884336c58bc")

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.VOICEMAIL_LEFT,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=contact_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        recipient_number="+**********",
        recipient_id=user_id,
        metadata={},
        external_id="TEST_CALL_SID",
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock organization preference with default template
    mock_organization_preferences = OrganizationPreferenceResponse(
        voice=OrganizationPreferenceDomainVoice(
            followup_email=OrganizationFollowupEmailPreferences(
                enabled=True, default_template_id=org_template_id
            )
        )
    )
    organization_preference_service.get_organization_preference.return_value = (
        mock_organization_preferences
    )

    # Mock user preference with a selected template
    mock_user_preferences = UserPreferenceResponse(
        voice=UserPreferenceDomainVoice(
            followup_email=UserFollowupEmailPreferences(
                selected_template_id=user_template_id
            ),
            voicemail=None,
        )
    )
    user_preference_service.get_user_preference.return_value = mock_user_preferences

    # Mock temporal client
    with patch(
        "salestech_be.core.voice.v2.voice_call_service.get_temporal_client"
    ) as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value = mock_client

        # Execute test
        await service._trigger_follow_up_email(call)

        # Verify feature flag check
        ff_service.is_enabled.assert_awaited_once()

        # Verify organization preference lookup
        organization_preference_service.get_organization_preference.assert_awaited_once()

        # Verify user preference lookup
        user_preference_service.get_user_preference.assert_awaited_once()

        # Verify temporal workflow start
        mock_client.start_workflow.assert_awaited_once()

        # Check workflow type and arguments
        workflow_args = mock_client.start_workflow.call_args
        assert workflow_args[0][0] == VoiceCallFollowUpEmailWorkflow.run

        # Check workflow arguments
        args = workflow_args[1]["args"]
        workflow_input = args[0]
        assert isinstance(workflow_input, VoiceCallFollowUpEmailWorkflowInput)
        assert workflow_input.call_id == call.id
        assert workflow_input.organization_id == organization_id
        assert workflow_input.user_id == user_id
        assert workflow_input.contact_id == contact_id
        # Should be using the user's template ID since it's available
        assert workflow_input.email_template_id == user_template_id
        assert workflow_input.account_id == account_id
        assert workflow_input.pipeline_id == pipeline_id

        # Check workflow ID
        assert workflow_args[1]["id"] == f"voice_call_send_follow_up_email_{call.id}"

        # Check task queue
        assert workflow_args[1]["task_queue"] == VOICE_TASK_QUEUE

        # Check reuse policy
        assert (
            workflow_args[1]["id_reuse_policy"]
            == WorkflowIDReusePolicy.REJECT_DUPLICATE
        )


async def test_trigger_follow_up_email_fallback_to_org_default(
    service: VoiceCallService,
    user_service: AsyncMock,
    contact_query_service: AsyncMock,
    email_account_service: AsyncMock,
    ff_service: AsyncMock,
    user_preference_service: AsyncMock,
    organization_preference_service: AsyncMock,
) -> None:
    """Test fallback to organization default template when user has no selection."""
    # Enable feature flag
    ff_service.is_enabled.return_value = True

    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    account_id = uuid4()
    pipeline_id = uuid4()
    call_id = uuid4()
    org_template_id = UUID("1193fa34-7c76-455d-bef8-1884336c58bc")

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.VOICEMAIL_LEFT,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=contact_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        recipient_number="+**********",
        recipient_id=user_id,
        metadata={},
        external_id="TEST_CALL_SID",
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock organization preference with default template
    mock_organization_preferences = OrganizationPreferenceResponse(
        voice=OrganizationPreferenceDomainVoice(
            followup_email=OrganizationFollowupEmailPreferences(
                enabled=True, default_template_id=org_template_id
            )
        )
    )
    organization_preference_service.get_organization_preference.return_value = (
        mock_organization_preferences
    )

    # Mock user preference with NO selected template
    mock_user_preferences = UserPreferenceResponse(
        voice=UserPreferenceDomainVoice(
            followup_email=UserFollowupEmailPreferences(selected_template_id=None),
            voicemail=None,
        )
    )
    user_preference_service.get_user_preference.return_value = mock_user_preferences

    # Mock temporal client
    with patch(
        "salestech_be.core.voice.v2.voice_call_service.get_temporal_client"
    ) as mock_get_client:
        mock_client = AsyncMock()
        mock_get_client.return_value = mock_client

        # Execute test
        await service._trigger_follow_up_email(call)

        # Verify feature flag check
        ff_service.is_enabled.assert_awaited_once()

        # Verify organization preference lookup
        organization_preference_service.get_organization_preference.assert_awaited_once()

        # Verify user preference lookup
        user_preference_service.get_user_preference.assert_awaited_once()

        # Verify temporal workflow start
        mock_client.start_workflow.assert_awaited_once()

        # Check workflow arguments
        args = mock_client.start_workflow.call_args[1]["args"]
        workflow_input = args[0]

        # Should be using the org's default template ID since user has no selection
        assert workflow_input.email_template_id == org_template_id


async def test_trigger_follow_up_email_already_running(
    service: VoiceCallService,
    user_service: AsyncMock,
    contact_query_service: AsyncMock,
    email_account_service: AsyncMock,
    ff_service: AsyncMock,
    user_preference_service: AsyncMock,
    organization_preference_service: AsyncMock,
) -> None:
    """Test handling of already running follow-up email workflow."""
    # Enable feature flag
    ff_service.is_enabled.return_value = True

    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    account_id = uuid4()
    pipeline_id = uuid4()
    call_id = uuid4()
    user_template_id = UUID("0093fa34-7c76-455d-bef8-1884336c58bc")
    org_template_id = UUID("1193fa34-7c76-455d-bef8-1884336c58bc")

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.VOICEMAIL_LEFT,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=contact_id,
        contact_id=contact_id,
        pipeline_id=pipeline_id,
        account_id=account_id,
        recipient_number="+**********",
        recipient_id=user_id,
        metadata={},
        external_id="TEST_CALL_SID",
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Mock organization preference with default template
    mock_organization_preferences = OrganizationPreferenceResponse(
        voice=OrganizationPreferenceDomainVoice(
            followup_email=OrganizationFollowupEmailPreferences(
                enabled=True, default_template_id=org_template_id
            )
        )
    )
    organization_preference_service.get_organization_preference.return_value = (
        mock_organization_preferences
    )

    # Mock user preference
    mock_user_preferences = UserPreferenceResponse(
        voice=UserPreferenceDomainVoice(
            followup_email=UserFollowupEmailPreferences(
                selected_template_id=user_template_id
            ),
            voicemail=None,
        )
    )
    user_preference_service.get_user_preference.return_value = mock_user_preferences

    # Mock temporal client with WorkflowAlreadyStartedError
    with patch(
        "salestech_be.core.voice.v2.voice_call_service.get_temporal_client"
    ) as mock_get_client:
        mock_client = AsyncMock()
        mock_client.start_workflow.side_effect = WorkflowAlreadyStartedError(
            workflow_type="VoiceCallFollowUpEmailWorkflow",
            workflow_id=f"voice_call_send_follow_up_email_{call.id}",
        )
        mock_get_client.return_value = mock_client

        # Execute test - should not raise an exception
        await service._trigger_follow_up_email(call)

        # Verify feature flag check
        ff_service.is_enabled.assert_awaited_once()

        # Verify organization preference lookup
        organization_preference_service.get_organization_preference.assert_awaited_once()

        # Verify user preference lookup
        user_preference_service.get_user_preference.assert_awaited_once()

        # Verify temporal workflow start attempt
        mock_client.start_workflow.assert_awaited_once()

        # Check workflow type and arguments
        workflow_args = mock_client.start_workflow.call_args
        assert workflow_args[0][0] == VoiceCallFollowUpEmailWorkflow.run

        # Check workflow ID
        assert workflow_args[1]["id"] == f"voice_call_send_follow_up_email_{call.id}"

        # Check task queue
        assert workflow_args[1]["task_queue"] == VOICE_TASK_QUEUE

        # Check reuse policy
        assert (
            workflow_args[1]["id_reuse_policy"]
            == WorkflowIDReusePolicy.REJECT_DUPLICATE
        )


async def test_get_call_success(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
) -> None:
    """Test getting a call by external ID successfully."""
    # Test data
    call_sid = "TEST_CALL_SID"
    call_id = uuid4()
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.COMPLETED,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=contact_id,
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
        disposition=CallDisposition.INTERESTED,
    )

    # Setup mock
    voice_call_repository.find_by_id.return_value = call

    # Execute test
    response = await service.get_call(call_id=call_id)

    # Verify result
    assert response.id == call_id
    assert response.status == CallStatus.COMPLETED
    assert response.disposition == CallDisposition.INTERESTED

    # Verify repository call
    voice_call_repository.find_by_id.assert_called_once_with(call_id)


async def test_get_call_not_found(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
) -> None:
    """Test getting a call that doesn't exist."""
    # Test data
    call_id = uuid4()

    # Setup mock
    voice_call_repository.find_by_id.return_value = None

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.get_call(call_id=call_id)

    assert str(exc_info.value) == "Call not found"

    # Verify repository call
    voice_call_repository.find_by_id.assert_called_once_with(call_id)


async def test_update_call_success(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test updating a call successfully."""
    # Test data
    call_sid = "TEST_CALL_SID"
    call_id = uuid4()
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    association_id = uuid4()
    pipeline_id = uuid4()
    # Mock original call object
    original_call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.COMPLETED,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=contact_id,
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
        disposition=None,
    )

    # Mock updated call object
    updated_call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=original_call.organization_phone_number_id,
        voice_provider_account_id=original_call.voice_provider_account_id,
        call_type=original_call.call_type,
        caller_number=original_call.caller_number,
        status=original_call.status,
        direction=original_call.direction,
        created_by_user_id=original_call.created_by_user_id,
        caller_id=original_call.caller_id,
        contact_id=original_call.contact_id,
        recipient_number=original_call.recipient_number,
        recipient_id=original_call.recipient_id,
        metadata=original_call.metadata,
        external_id=original_call.external_id,
        created_at=original_call.created_at,
        updated_at=original_call.updated_at,
        disposition=CallDisposition.INTERESTED,
    )

    # Mock domain CRM association with proper model_fields
    mock_association = DomainCRMAssociation(
        id=association_id,
        domain_type=DomainType.VOICE_CALL,
        organization_id=organization_id,
        call_id=call_id,
        contact_id=contact_id,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Setup mocks
    voice_call_repository.find_by_id.return_value = original_call
    voice_call_repository.update_by_id.return_value = updated_call
    domain_crm_association_service.get_domain_crm_associations_by_related_id.return_value = [
        mock_association
    ]
    domain_crm_association_service.update_domain_crm_association.return_value = None

    # Create update request
    update_request = UpdateCallRequest(
        disposition=CallDisposition.INTERESTED,
        pipeline_id=pipeline_id,
    )

    # Execute test
    response = await service.update_call(
        call_id=call_id,
        request=update_request,
    )

    # Verify result
    assert response.id == call_id
    assert response.disposition == CallDisposition.INTERESTED

    # Verify repository calls
    voice_call_repository.find_by_id.assert_called_once_with(call_id)
    voice_call_repository.update_by_id.assert_called_once_with(
        call_id,
        column_to_update=update_request.model_dump(exclude_unset=True),
    )

    # Verify domain CRM association calls
    domain_crm_association_service.get_domain_crm_associations_by_related_id.assert_called_once_with(
        domain_type=DomainType.VOICE_CALL, domain_id=call_id
    )

    # Verify the update call with correct params
    domain_crm_association_service.update_domain_crm_association.assert_called_once()
    call_args = domain_crm_association_service.update_domain_crm_association.call_args[
        1
    ]
    assert call_args["association_id"] == association_id
    assert "domain_crm_association" in call_args
    assert call_args["domain_crm_association"].pipeline_id == pipeline_id


async def test_update_call_not_found(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test updating a call that doesn't exist."""
    # Test data
    call_id = uuid4()

    # Setup mock
    voice_call_repository.find_by_id.return_value = None

    # Create update request
    update_request = UpdateCallRequest(
        disposition=CallDisposition.INTERESTED,
    )

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.update_call(
            call_id=call_id,
            request=update_request,
        )

    assert str(exc_info.value) == "Call not found"

    # Verify repository call
    voice_call_repository.find_by_id.assert_called_once_with(call_id)
    voice_call_repository.update_by_id.assert_not_called()

    # Verify domain CRM association call not made
    domain_crm_association_service.get_domain_crm_associations_by_related_id.assert_not_called()
    domain_crm_association_service.update_domain_crm_association.assert_not_called()


async def test_update_call_update_fails(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test updating a call where the update operation fails."""
    # Test data
    call_sid = "TEST_CALL_SID"
    call_id = uuid4()
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.COMPLETED,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=user_id,
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=contact_id,
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Setup mocks
    voice_call_repository.find_by_id.return_value = call
    voice_call_repository.update_by_id.return_value = None  # Update fails

    # Create update request
    update_request = UpdateCallRequest(
        disposition=CallDisposition.INTERESTED,
    )

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.update_call(
            call_id=call_id,
            request=update_request,
        )

    assert str(exc_info.value) == "Call not found"

    # Verify repository calls
    voice_call_repository.find_by_id.assert_called_once_with(call_id)
    voice_call_repository.update_by_id.assert_called_once_with(
        call_id,
        column_to_update=update_request.model_dump(exclude_unset=True),
    )

    # Verify domain CRM association calls not made
    domain_crm_association_service.get_domain_crm_associations_by_related_id.assert_not_called()
    domain_crm_association_service.update_domain_crm_association.assert_not_called()


async def test_update_call_no_domain_crm_association(
    service: VoiceCallService,
    voice_call_repository: AsyncMock,
    domain_crm_association_service: AsyncMock,
) -> None:
    """Test updating a call where no domain CRM association exists."""
    # Test data
    call_sid = "TEST_CALL_SID"
    call_id = uuid4()
    organization_id = uuid4()

    # Mock call object
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.COMPLETED,
        direction=CallDirection.OUTBOUND,
        created_by_user_id=uuid4(),
        caller_id=uuid4(),
        contact_id=uuid4(),
        recipient_number="+**********",
        recipient_id=uuid4(),
        metadata={},
        external_id=call_sid,
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Setup mocks
    voice_call_repository.find_by_id.return_value = call
    voice_call_repository.update_by_id.return_value = call  # Update succeeds
    domain_crm_association_service.get_domain_crm_associations_by_related_id.return_value = []  # No associations

    # Create update request
    update_request = UpdateCallRequest(
        disposition=CallDisposition.INTERESTED,
    )

    # Execute test and verify exception
    with pytest.raises(ResourceNotFoundError) as exc_info:
        await service.update_call(
            call_id=call_id,
            request=update_request,
        )

    assert str(exc_info.value) == "Domain CRM association not found"

    # Verify repository calls
    voice_call_repository.find_by_id.assert_called_once_with(call_id)
    voice_call_repository.update_by_id.assert_called_once_with(
        call_id,
        column_to_update=update_request.model_dump(exclude_unset=True),
    )

    # Verify domain CRM association lookup with proper typing
    domain_crm_association_service.get_domain_crm_associations_by_related_id.assert_called_once_with(
        domain_type=DomainType.VOICE_CALL, domain_id=call_id
    )
    domain_crm_association_service.update_domain_crm_association.assert_not_called()


async def test_trigger_follow_up_email_skipped_for_call_from_task(
    service: VoiceCallService,
    ff_service: AsyncMock,
    user_preference_service: AsyncMock,
    organization_preference_service: AsyncMock,
) -> None:
    """Test that follow-up email is not triggered when call is from a task."""
    # Enable feature flag
    ff_service.is_enabled.return_value = True

    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    call_id = uuid4()
    task_id = uuid4()

    # Mock call object with task_id in metadata
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.VOICEMAIL_LEFT,  # Status that would normally trigger follow-up
        direction=CallDirection.OUTBOUND,
        created_by_user_id=user_id,
        caller_id=contact_id,
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=user_id,
        metadata={"task_id": task_id},  # Call initiated from a task
        external_id="TEST_CALL_SID",
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Execute test
    await service._trigger_follow_up_email(call)

    # Verify feature flag was checked
    ff_service.is_enabled.assert_awaited_once()

    # Verify no further processing was done - these services should not be called
    user_preference_service.get_user_preference.assert_not_awaited()
    organization_preference_service.get_organization_preference.assert_not_awaited()

    # No need to patch get_temporal_client as the function should return early


async def test_trigger_follow_up_email_skipped_for_inbound_calls(
    service: VoiceCallService,
    ff_service: AsyncMock,
    user_preference_service: AsyncMock,
    organization_preference_service: AsyncMock,
) -> None:
    """Test that follow-up email is not triggered for inbound calls."""
    # Enable feature flag
    ff_service.is_enabled.return_value = True

    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    contact_id = uuid4()
    call_id = uuid4()

    # Mock call object with INBOUND direction
    call = Call(
        id=call_id,
        organization_id=organization_id,
        organization_phone_number_id=uuid4(),
        voice_provider_account_id=uuid4(),
        call_type=CallType.WEBRTC,
        caller_number="+**********",
        status=CallStatus.VOICEMAIL_LEFT,  # Status that would normally trigger follow-up
        direction=CallDirection.INBOUND,  # Inbound call should skip follow-up
        created_by_user_id=user_id,
        caller_id=contact_id,
        contact_id=contact_id,
        recipient_number="+**********",
        recipient_id=user_id,
        metadata={},
        external_id="TEST_CALL_SID",
        created_at=TEST_DATETIME,
        updated_at=TEST_DATETIME,
    )

    # Execute test
    await service._trigger_follow_up_email(call)

    # Verify feature flag was checked
    ff_service.is_enabled.assert_awaited_once()

    # Verify no further processing was done - these services should not be called
    user_preference_service.get_user_preference.assert_not_awaited()
    organization_preference_service.get_organization_preference.assert_not_awaited()

    # No need to patch get_temporal_client as the function should return early

    # No need to patch get_temporal_client as the function should return early


async def test_get_usage_record(
    service: VoiceCallService,
    provider_client: AsyncMock,
) -> None:
    """Test getting usage records for a voice service."""
    # Test data
    organization_id = uuid4()
    user_id = uuid4()
    provider = "twilio"
    start_date = datetime(2023, 1, 1, tzinfo=UTC)
    end_date = datetime(2023, 1, 31, tzinfo=UTC)
    categories = [VoiceUsageCategory.TOTAL_PRICE, VoiceUsageCategory.CALLS]

    # Mock response from provider client
    mock_usage_response = UsageRecordsResponse(
        success=True,
        usage_records=[
            {
                "category": "calls",
                "description": "Outbound calls",
                "account_sid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "price": "12.50",
                "price_unit": "usd",
            },
            {
                "category": "sms",
                "description": "SMS messages",
                "account_sid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "price": "5.25",
                "price_unit": "usd",
            },
            {
                "category": "totalprice",
                "description": "Total price",
                "account_sid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                "price": "17.75",
                "price_unit": "usd",
            },
        ],
        uri="/2010-04-01/Accounts/ACxxxxxxx/Usage/Records",
    )
    provider_client.get_usage_record.return_value = mock_usage_response

    # Execute test
    result = await service.get_usage_record(
        organization_id=organization_id,
        user_id=user_id,
        provider=provider,
        start_date=start_date,
        end_date=end_date,
        category=categories,
    )

    # Verify result
    assert result == mock_usage_response

    # Verify provider client method was called with correct parameters
    provider_client.get_usage_record.assert_called_once_with(
        organization_id=organization_id,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
        category=categories,
    )
