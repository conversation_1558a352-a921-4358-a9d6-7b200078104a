from __future__ import annotations

from typing import Any, Literal

from pydantic import BaseModel, Field

################################
# Style Config
################################


class StyleConfig(BaseModel):
    """Custom CSS style configuration"""

    background_color: str | None = None
    color: str | None = None
    border_radius: str | None = None
    box_shadow: str | None = None
    padding: str | None = None
    margin: str | None = None
    border: str | None = None
    font_size: str | None = None
    font_weight: str | None = None
    font_family: str | None = None
    text_align: str | None = None
    line_height: str | None = None
    letter_spacing: str | None = None
    text_transform: str | None = None
    text_decoration: str | None = None
    opacity: float | None = None

    # Allow additional CSS properties
    additional_properties: dict[str, Any] = Field(default_factory=dict)


################################
# Dashboard Layout Config
################################


class GridBreakpoints(BaseModel):
    """Breakpoints for responsive grid layout"""

    lg: int = 1200
    md: int = 996
    sm: int = 768
    xs: int = 480


class GridConfig(BaseModel):
    """Configuration for grid layout"""

    cols: int = 24
    row_height: int = 30
    breakpoints: GridBreakpoints = Field(default_factory=GridBreakpoints)


class DashboardLayoutConfig(BaseModel):
    """Configuration for dashboard layout"""

    grid: GridConfig = Field(default_factory=GridConfig)
    style: StyleConfig | None = None


################################
# Dashboard Chart Layout Config
################################


class PositionConfig(BaseModel):
    """Position configuration for a chart within a dashboard"""

    x: int = 0  # col start
    y: int = 0  # row start
    w: int = 8  # width in columns
    h: int = 6  # height in rows
    is_resizable: bool = True
    min_w: int | None = None
    max_w: int | None = None
    min_h: int | None = None
    max_h: int | None = None


class DashboardChartLayoutConfig(BaseModel):
    """Configuration for a chart layout within a dashboard"""

    type: str = "chart"  # chart, table, text, etc.
    position: PositionConfig = Field(default_factory=PositionConfig)
    style: StyleConfig | None = None


################################
# Chart Layout Config
################################


class ChartConfig(BaseModel):
    """Configuration for chart visualization"""

    chart_type: str  # bar, line, pie, etc.
    x_axis: str | None = None
    y_axis: list[str] | None = None
    series: list[str] | None = None
    stacked: bool = False
    show_legend: bool = True
    show_grid: bool = True
    show_tooltip: bool = True
    color_scheme: list[str] | None = None


class TableConfig(BaseModel):
    """Configuration for table visualization"""

    columns: list[str]
    sortable: bool = True
    filterable: bool = True
    pagination: bool = True
    page_size: int = 10
    show_header: bool = True
    show_footer: bool = False
    row_hover: bool = True
    striped: bool = False
    bordered: bool = True


class TextConfig(BaseModel):
    """Configuration for text visualization"""

    content: str
    format: Literal["markdown", "html", "plain"] = "markdown"


class VisualizationConfig(BaseModel):
    """Configuration for chart visualization"""

    type: str  # chart, table, text, etc.
    title: str | None = None
    subtitle: str | None = None
    description: str | None = None
    chart: ChartConfig | None = None
    table: TableConfig | None = None
    text: TextConfig | None = None


class ChartLayoutConfig(BaseModel):
    """Configuration for chart layout"""

    visualization: VisualizationConfig
    style: StyleConfig | None = None
