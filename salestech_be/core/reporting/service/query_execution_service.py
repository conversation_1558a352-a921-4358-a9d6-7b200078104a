from typing import Any
from uuid import UUID

from salestech_be.core.reporting.connection.postgres_dwh_connection import (
    AsyncPostgresDwhConnection,
)
from salestech_be.core.reporting.service.query_builder_service import (
    QueryBuilderService,
)
from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import QueryConfig
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger


class QueryExecutionService:
    def __init__(
        self,
        db_engine: DatabaseEngine,
        dwh_connection: AsyncPostgresDwhConnection,
    ):
        self.db_engine = db_engine
        self.dwh_connection = dwh_connection
        self.reporting_repository = ReportingRepository(engine=db_engine)
        self.query_builder_service = QueryBuilderService(db_engine)
        self.logger = get_logger()

    async def execute_query(
        self,
        *,
        query_config: QueryConfig,
        organization_id: UUID,
    ) -> list[dict[str, Any]]:
        """
        Execute a query directly from a QueryConfig.
        """
        query = self.query_builder_service.build_query(
            query_config=query_config,
            organization_id=organization_id,
        )
        self.logger.bind(query_config=query_config, query=query).info("Executing query")
        return await self.dwh_connection.execute_query(query)

    async def execute_dataset(
        self,
        *,
        dataset_id: UUID,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> list[dict[str, Any]]:
        """
        Execute a query for a dataset.
        """
        query = await self.query_builder_service.build_dataset_query(
            dataset_id=dataset_id,
            filters=filters,
            organization_id=organization_id,
        )
        self.logger.bind(
            dataset_id=dataset_id,
            filters=filters,
            query=query,
        ).info("Executing dataset query")
        return await self.dwh_connection.execute_query(query)

    async def execute_chart(
        self,
        *,
        chart_id: UUID,
        filters: list[Filter] | None = None,
        organization_id: UUID,
    ) -> list[dict[str, Any]]:
        """
        Execute a query for a chart.
        """
        query = await self.query_builder_service.build_chart_query(
            chart_id=chart_id,
            filters=filters,
            organization_id=organization_id,
        )
        self.logger.bind(
            chart_id=chart_id,
            filters=filters,
            query=query,
        ).info("Executing chart query")
        return await self.dwh_connection.execute_query(query)
