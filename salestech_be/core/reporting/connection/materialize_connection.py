import logging
from typing import Any

import asyncpg


class AsyncMaterializeConnection:
    """Async connection manager for Materialize database"""

    def __init__(
        self,
        host: str,
        port: int,
        user: str,
        password: str,
        database: str,
        ssl: bool = False,
    ):
        """
        Initialize an async connection to Materialize

        Args:
            host: Materialize host
            port: Materialize port (default 6875)
            user: Database user
            password: Database password
            database: Database name
        """
        self.connection_params = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database,
            "ssl": ssl,
        }
        self.connection = None
        self.logger = logging.getLogger(__name__)

    async def connect(self) -> bool:
        """
        Establish an async connection to Materialize

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.connection = await asyncpg.connect(**self.connection_params)
            self.logger.info(
                f"Connected to Materialize at {self.connection_params['host']}:{self.connection_params['port']}"
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to Materialize: {e!s}")
            return False

    async def disconnect(self) -> None:
        """Close the async connection to Materialize"""
        if self.connection:
            await self.connection.close()
            self.connection = None
            self.logger.info("Disconnected from Materialize")

    async def execute_query(
        self, query: str, params: tuple | None = None
    ) -> list[dict[str, Any]]:
        """
        Execute a query against Materialize asynchronously

        Args:
            query: SQL query to execute
            params: Query parameters (for parameterized queries)

        Returns:
            List of dictionaries containing the query results

        Raises:
            ConnectionError: If not connected to Materialize
            asyncpg.exceptions.PostgresError: For database-related errors
            Exception: For other errors during query execution
        """
        if not self.connection:
            success = await self.connect()
            if not success:
                raise ConnectionError("Failed to connect to Materialize")

        try:
            # Execute the query
            self.logger.debug(f"Executing query: {query}")

            # Convert params to a list if it's a tuple
            param_list = list(params) if params else None

            # For SELECT queries, fetch results
            if query.strip().upper().startswith(
                "SELECT"
            ) or query.strip().upper().startswith("WITH"):
                rows = await self.connection.fetch(
                    query, *param_list if param_list else []
                )
                return [dict(row) for row in rows]
            else:
                # For non-SELECT queries, just execute
                await self.connection.execute(query, *param_list if param_list else [])
                return []

        except asyncpg.exceptions.PostgresError as e:
            self.logger.error(f"Database error: {e!s}")
            # Try to reconnect once on connection errors
            if "connection" in str(e).lower():
                try:
                    await self.disconnect()
                    await self.connect()
                    # Retry once
                    return await self.execute_query(query, params)
                except Exception as reconnect_error:
                    self.logger.error(f"Reconnection failed: {reconnect_error!s}")
                    raise
            raise
        except Exception as e:
            self.logger.error(f"Query execution error: {e!s}")
            raise

    async def get_table_schema(self, table_name: str) -> dict[str, str]:
        """
        Get the schema for a table asynchronously

        Args:
            table_name: Name of the table

        Returns:
            Dictionary mapping column names to their data types
        """
        query = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = $1
        """
        results = await self.execute_query(query, (table_name,))

        return {row["column_name"]: row["data_type"] for row in results}

    async def list_tables(self) -> list[str]:
        """
        List all tables in the current database asynchronously

        Returns:
            List of table names
        """
        query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        """
        results = await self.execute_query(query)

        return [row["table_name"] for row in results]

    async def __aenter__(self):
        """Support for async context manager pattern"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Support for async context manager pattern"""
        await self.disconnect()
