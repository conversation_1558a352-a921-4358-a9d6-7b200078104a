from __future__ import annotations

from datetime import date, time
from enum import StrEnum
from typing import Annotated, Any, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.db.models.core.base import (
    Column,
    DBModel,
    JsonColumn,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class SequenceStatus(StrEnum):
    DRAFT = "DRAFT"
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    TERMINATED = "TERMINATED"


class SequenceScanStatus(StrEnum):
    SCANNING = "SCANNING"
    IDLE = "IDLE"
    ERROR = "ERROR"


class SequenceStepStatus(StrEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class SequenceStepBranchType(StrEnum):
    TRIGGER_EVENT = "TRIGGER_EVENT"
    DEFAULT = "DEFAULT"


class SequenceScheduleType(StrEnum):
    CUSTOMIZED = "CUSTOMIZED"
    GLOBAL = "GLOBAL"


class SequenceStatsDb(DBModel):
    scheduled_count: int
    delivered_count: int
    bounced_count: int
    reply_count: int
    opt_out_count: int
    opened_count: int
    link_clicked_count: int


class SequenceStatsBySequenceId(SequenceStatsDb):
    sequence_id: UUID


class SequenceStatsBySequenceStepId(SequenceStatsDb):
    sequence_step_id: UUID


class SequenceStatsBySequenceStepVariantId(SequenceStatsDb):
    sequence_step_variant_id: UUID


class Timezone(StrEnum):
    UTC = "UTC"
    PST = "America/Los_Angeles"  # Pacific Time
    EST = "America/New_York"  # Eastern Time
    CST = "America/Chicago"  # Central Time
    MST = "America/Denver"  # Mountain Time
    HST = "Pacific/Honolulu"  # Hawaii-Aleutian Time


class DayOfWeek(StrEnum):
    SUN = "SUN"
    MON = "MON"
    TUE = "TUE"
    WED = "WED"
    THU = "THU"
    FRI = "FRI"
    SAT = "SAT"

    def __lt__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if isinstance(other, DayOfWeek):
            return self.value < other.value
        return NotImplemented

    def get_weekday(self) -> int:
        return {
            self.SUN: 6,
            self.MON: 0,
            self.TUE: 1,
            self.WED: 2,
            self.THU: 3,
            self.FRI: 4,
            self.SAT: 5,
        }[self]


class SequenceTargetStatus(StrEnum):
    ACTIVE = "ACTIVE"
    PAUSED = "PAUSED"
    FINISHED = "FINISHED"
    FAILED = "FAILED"
    BOUNCED = "BOUNCED"
    UNSUBSCRIBED = "UNSUBSCRIBED"
    ERROR = "ERROR"
    ARCHIVED = "ARCHIVED"

    def is_reachable(self) -> bool:
        return self.value == self.ACTIVE


class SequenceStepTriggerEvent(StrEnum):
    EMAIL_REPLY = "EMAIL_REPLY"
    TASK_COMPLETE = "TASK_COMPLETE"


class SequenceStepActionType(StrEnum):
    EMAIL = "EMAIL"
    TASK = "TASK"


class SequenceStepActionEmailType(StrEnum):
    NEW = "NEW"
    CONTINUE_IN_THREAD = "CONTINUE_IN_THREAD"


class SequenceExecutionStatus(StrEnum):
    # execution need to be rescheduled due to some reason
    # e.g. email rate limit reached, out of delivery window
    # Execution in this status will be picked up by the
    # evaluator again in the next evaluation cycle
    RESCHEDULE = "RESCHEDULE"
    # enqueued into sequence async execution system
    PENDING = "PENDING"
    # execution failed
    FAILURE = "FAILURE"
    # sequence async execution system has called email to create campaign
    EMAIL_QUEUED = "EMAIL_QUEUED"
    # sequence email has been scheduled, but hasn't been sent yet
    EMAIL_SCHEDULED = "EMAIL_SCHEDULED"
    # 3rd party email system has accepted email
    EMAIL_SEND_ATTEMPTED = "EMAIL_SEND_ATTEMPTED"
    # email webhook send failure
    EMAIL_SEND_FAILURE = "EMAIL_SEND_FAILURE"
    # task created
    TASK_CREATED = "TASK_CREATED"
    # email replied
    EMAIL_REPLIED = "EMAIL_REPLIED"
    # task completed
    TASK_COMPLETED = "TASK_COMPLETED"
    # cancelled execution, use cancel instead of delete
    # since execution has unique constraint on (sequence_id, target_id, step_id)
    CANCELLED = "CANCELLED"
    # execution/target has error
    ERROR = "ERROR"
    # email unsubscribed
    UNSUBSCRIBED = "UNSUBSCRIBED"

    def is_failed(self) -> bool:
        return self in (self.FAILURE, self.EMAIL_SEND_FAILURE)

    def is_executed(self) -> bool:
        return self in (
            self.EMAIL_QUEUED,
            self.EMAIL_SEND_ATTEMPTED,
            self.EMAIL_REPLIED,
            self.TASK_CREATED,
            self.TASK_COMPLETED,
        )

    def is_need_reschedule(self) -> bool:
        return self in (self.RESCHEDULE, self.CANCELLED, self.ERROR, self.PENDING)


class EmailEventType(StrEnum):
    SEND_ATTEMPTED = "SEND_ATTEMPTED"
    SEND_ATTEMPTED_FAILED = "SEND_ATTEMPTED_FAILED"
    BOUNCE_DETECTED = "BOUNCE_DETECTED"
    OPENED = "OPENED"
    LINK_CLICKED = "LINK_CLICKED"
    REPLIED = "REPLIED"
    UNSUBSCRIBED = "UNSUBSCRIBED"


class TargetStatusChangeSource(StrEnum):
    EMAIL_EVENT = "EMAIL_EVENT"
    USER = "USER"
    SEQUENCE_EVALUATOR = "SEQUENCE_EVALUATOR"
    SCHEDULER_BOOKING_EVENT = "SCHEDULER_BOOKING_EVENT"


class SequenceActionStatus(StrEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class SequencePermissionEnum(StrEnum):
    PRIVATE = "PRIVATE"
    TEAM_CAN_VIEW = "TEAM_CAN_VIEW"
    TEAM_CAN_USE = "TEAM_CAN_USE"

    def is_shared(self) -> bool:
        return self in (self.TEAM_CAN_VIEW, self.TEAM_CAN_USE)


class SequenceUserPermissionEnum(StrEnum):
    CAN_CHANGE_SEQUENCE_PERMISSION = "CAN_CHANGE_SEQUENCE_PERMISSION"
    CAN_VIEW = "CAN_VIEW"
    CAN_ADD_TARGET = "CAN_ADD_TARGET"
    CAN_UPDATE_SEQUENCE = "CAN_UPDATE_SEQUENCE"


class SequencePermissionResourceType(StrEnum):
    SEQUENCE = "SEQUENCE"
    SEQUENCE_STEP = "SEQUENCE_STEP"
    SEQUENCE_ACTION = "SEQUENCE_ACTION"
    SEQUENCE_TARGET = "SEQUENCE_TARGET"


class SequencePermissionAction(StrEnum):
    CHANGE_PERMISSION = "CHANGE_PERMISSION"
    VIEW = "VIEW"
    UPDATE = "UPDATE"
    ADD_TARGET = "ADD_TARGET"


class SequenceDefaultOwnerCfg(StrEnum):
    SEQUENCE_OWNER = "SEQUENCE_OWNER"
    CONTACT_OWNER = "CONTACT_OWNER"


class SequenceDefaultEmailAccountCfg(StrEnum):
    SEQUENCE_OWNER = "SEQUENCE_OWNER"
    CONTACT_OWNER = "CONTACT_OWNER"
    TARGET_OWNER = "TARGET_OWNER"


class SequenceErrorCode(StrEnum):
    # Integration related
    USER_INTEGRATION_ERROR = "USER_INTEGRATION_ERROR"

    # User error
    OWNER_USER_NOT_EXIST = "OWNER_USER_NOT_EXIST"
    EMAIL_ACCOUNT_NOT_EXIST = "EMAIL_ACCOUNT_NOT_EXIST"
    EMAIL_ACCOUNT_NOT_AVAILABLE = "EMAIL_ACCOUNT_NOT_EXIST"

    # Template and Message related
    TEMPLATE_RENDER_ERROR = "TEMPLATE_RENDER_ERROR"
    TEMPLATE_SUBJECT_MISSING = "TEMPLATE_SUBJECT_MISSING"
    TEMPLATE_BODY_MISSING = "TEMPLATE_BODY_MISSING"
    MESSAGE_SCHEDULED_FAILURE = "MESSAGE_SCHEDULED_FAILURE"
    MESSAGE_SEND_FAILURE = "MESSAGE_SEND_FAILURE"
    CONTACT_NOT_EXIST = "CONTACT_NOT_EXIST"
    CONTACT_EMAIL_MISSING = "CONTACT_EMAIL_MISSING"
    # Target related
    TARGET_EMAIL_MISSING = "TARGET_EMAIL_MISSING"
    TARGET_EMAIL_ACCOUNT_MISSING = "TARGET_EMAIL_ACCOUNT_MISSING"

    # Step error
    UNSUPPORTED_STEP_TYPE = "UNSUPPORTED_STEP_TYPE"

    # Task related
    TASK_CREATION_FAILED = "TASK_CREATION_FAILED"

    # System error
    SYSTEM_ERROR = "SYSTEM_ERROR"

    @classmethod
    def template_errors(cls) -> list[SequenceErrorCode]:
        return [
            cls.TEMPLATE_RENDER_ERROR,
            cls.TEMPLATE_SUBJECT_MISSING,
            cls.TEMPLATE_BODY_MISSING,
        ]

    @classmethod
    def target_errors(cls) -> list[SequenceErrorCode]:
        return [
            cls.TARGET_EMAIL_MISSING,
            cls.TARGET_EMAIL_ACCOUNT_MISSING,
        ]

    def is_template_error(self) -> bool:
        return self in self.template_errors()

    def is_target_error(self) -> bool:
        return self in self.target_errors()


class SequenceErrorStatus(StrEnum):
    NEW = "NEW"
    RESOLVED = "RESOLVED"


class SequenceErrorResourceType(StrEnum):
    TARGET = "TARGET"
    TEMPLATE = "TEMPLATE"
    EMAIL_ACCOUNT = "EMAIL_ACCOUNT"


# TODO(REEVO-212) remove table
class SequenceList(TableModel):
    table_name = TableName.sequence_list
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]
    owner_user_id: Column[UUID]


class Sequence(TableModel):
    table_name = TableName.sequence
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    unsubscription_group_id: Column[UUID | None] = None
    schedule_id: Column[UUID | None] = None
    status: Column[SequenceStatus]
    last_activated_at: Column[ZoneRequiredDateTime | None] = None
    owner_user_id: Column[UUID]
    email_account_pool_id: Column[UUID | None] = None
    scan_status: Column[SequenceScanStatus | None] = None
    last_scan_started_at: Column[ZoneRequiredDateTime | None] = None
    last_scan_finished_at: Column[ZoneRequiredDateTime | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]
    permission: Column[SequencePermissionEnum | None] = None
    default_owner_configuration: Column[SequenceDefaultOwnerCfg]
    default_email_account_configuration: Column[SequenceDefaultEmailAccountCfg]
    is_template: Column[bool | None] = None


# TODO(REEVO-212) remove table
class SequenceListMembership(TableModel):
    table_name = TableName.sequence_list_membership
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    sequence_list_id: Column[UUID]
    contact_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]


class SequenceTarget(TableModel):
    table_name = TableName.sequence_target
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    sequence_id: Column[UUID]
    contact_id: Column[UUID]
    sequence_list_id: Column[UUID | None] = None
    sequence_list_membership_id: Column[UUID | None] = None
    status: Column[SequenceTargetStatus]
    status_change_source: Column[TargetStatusChangeSource | None] = None
    last_sequence_step_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]
    owner_user_id: Column[UUID]
    email_sender_user_id: Column[UUID]  # to be removed soon
    email_account_id: Column[UUID | None] = None


class SequenceStep(TableModel):
    table_name = TableName.sequence_step
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    sequence_id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    is_first_step: Column[bool]
    interval: Column[int]
    status: Column[SequenceStepStatus]
    trigger_event: Column[str | None] = None
    trigger_event_next_step_id: Column[UUID | None] = None
    default_next_step_id: Column[UUID | None] = None
    action_type: Column[SequenceStepActionType]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]


class SequenceAction(TableModel):
    table_name = TableName.sequence_action
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    status: Column[SequenceActionStatus]
    template_id: Column[UUID]
    email_type: Column[SequenceStepActionEmailType | None] = None
    sequence_step_id: Column[UUID]
    sequence_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]


class UpsertSequenceStep:
    step: SequenceStepV2
    is_new: bool = False

    def __init__(self, step: SequenceStepV2, is_new: bool = False):
        self.step = step
        self.is_new = is_new


class SequenceScheduleTime(BaseModel):
    day_of_the_week: Column[DayOfWeek]
    start_time: Column[time]
    end_time: Column[time]
    is_active: Column[bool] = True


class SequenceSchedule(TableModel):
    table_name = TableName.sequence_schedule
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    type: Column[SequenceScheduleType]
    use_target_profile_timezone: Column[bool]
    default_timezone: Column[Timezone]
    skip_holidays: Column[bool]
    schedule_times: JsonColumn[list[SequenceScheduleTime] | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]


class SequenceExecution(TableModel):
    table_name = TableName.sequence_execution
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    sequence_id: Column[UUID]
    step_id: Column[UUID]
    target_id: Column[UUID]
    action_id: Column[UUID | None] = None
    status: Column[SequenceExecutionStatus]
    email_address: Column[str | None] = None
    triggered_by_user_id: Column[UUID | None] = None
    email_id: Column[UUID | None] = None
    email_send_at: Column[ZoneRequiredDateTime | None] = None
    task_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    organization_id: Column[UUID]


class EmailProcessingStatus(StrEnum):
    PENDING = "PENDING"
    PROCESSED = "PROCESSED"
    FAILED = "FAILED"


class EmailEvent(TableModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    table_name = TableName.email_event
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    email_account_id: Column[UUID | None] = None
    event_time: Column[ZoneRequiredDateTime]
    type: Column[EmailEventType]
    details: JsonColumn[dict[str, Any] | None] = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    processing_status: Column[EmailProcessingStatus]
    organization_id: Column[UUID]
    # TODO: link: remove None for the following columns after claan up all the email events
    global_thread_id: Column[UUID | None] = None
    global_message_id: Column[UUID | None] = None
    external_event_id: Column[str | None] = None


class EmailEventsByDate(DBModel):
    date: date
    step_id: UUID
    type: EmailEventType
    count: int


class EmailEvents(DBModel):
    step_id: UUID
    type: EmailEventType
    count: int


class TargetCounts(DBModel):
    count: int
    step_id: UUID | None
    type: EmailEventType | None


class SequenceEmailEventActivity(DBModel):
    email_id: UUID
    type: EmailEventType
    last_activity_at: ZoneRequiredDateTime


class SequenceTargetEmailStatus(SequenceTarget):
    primary_email: str | None
    primary_phone_number: str | None
    display_name: str | None
    email_status: EmailEventType | None
    step_id: UUID | None
    action_id: UUID | None
    last_activity_at: ZoneRequiredDateTime


class SequenceActionDto(SequenceAction):
    step_action_type: SequenceStepActionType


class SequenceVisibility(NameValueStrEnum):
    TEAM_VIEWABLE = "TEAM_VIEWABLE"
    TEAM_EDITABLE = "TEAM_EDITABLE"
    PRIVATE = "PRIVATE"


class SequenceParticipant(BaseModel):
    user_id: UUID
    role: str = "editor"


class SequenceStepManualFlowControl(NameValueStrEnum):
    # Manually Complete the Task and auto Resume / Continue the Sequence
    COMPLETE_AND_CONTINUE = "COMPLETE_AND_CONTINUE"

    # Manually Complete the Task and manually Resume / Continue the Sequence
    COMPLETE_AND_PAUSE = "COMPLETE_AND_PAUSE"

    # This is effectlly making the step a last step
    # Included here for completeness
    COMPLETE_AND_STOP = "COMPLETE_AND_STOP"


class SequenceStepType(StrEnum):
    AUTO_EMAIL = "AUTO_EMAIL"
    MANUAL_EMAIL = "MANUAL_EMAIL"
    CALL_TASK = "CALL_TASK"
    LINKEDIN_MESSAGE_TASK = "LINKEDIN_MESSAGE_TASK"
    LINKEDIN_CONNECTION_REQUEST_TASK = "LINKEDIN_CONNECTION_REQUEST_TASK"
    LINKEDIN_INMAIL_TASK = "LINKEDIN_INMAIL_TASK"

    @classmethod
    def get_manual_task_types(cls) -> list[SequenceStepType]:
        return [
            cls.MANUAL_EMAIL,
            cls.CALL_TASK,
            cls.LINKEDIN_MESSAGE_TASK,
            cls.LINKEDIN_CONNECTION_REQUEST_TASK,
            cls.LINKEDIN_INMAIL_TASK,
        ]


class SequenceFailureReason(NameValueStrEnum):
    CONTACT_ALREADY_ENROLLED = "CONTACT_ALREADY_ENROLLED"
    CONTACT_ALREADY_EXITED = "CONTACT_ALREADY_EXITED"
    CONTACT_ALREADY_FAILED = "CONTACT_ALREADY_FAILED"
    CONTACT_ALREADY_REMOVED = "CONTACT_ALREADY_REMOVED"

    EMAIL_UNSUBSCRIBED = "EMAIL_UNSUBSCRIBED"

    CONTACT_NOT_ELIGIBLE = "CONTACT_NOT_ELIGIBLE"
    CONTACT_DOES_NOT_EXIST = "CONTACT_DOES_NOT_EXIST"
    ACCOUNT_DOES_NOT_EXIST = "ACCOUNT_DOES_NOT_EXIST"

    EMAIL_ACCOUNT_NOT_AVAILABLE = "EMAIL_ACCOUNT_NOT_AVAILABLE"
    MAILBOXES_NOT_WARMED_UP = "MAILBOXES_NOT_WARMED_UP"

    NO_EMAIL_FOUND_FOR_CONTACT = "NO_EMAIL_FOUND_FOR_CONTACT"
    NO_PRIMARY_ACCOUNT_FOUND = "NO_PRIMARY_ACCOUNT_FOUND"
    EMAIL_NOT_ASSOCIATED_WITH_CONTACT = "EMAIL_NOT_ASSOCIATED_WITH_CONTACT"

    COULD_NOT_RESOLVE_ACCOUNT_FOR_CONTACT = "COULD_NOT_RESOLVE_ACCOUNT_FOR_CONTACT"


class SequenceV2Schedule(BaseModel):
    timezone: str
    skip_holidays: bool
    schedule_times: list[SequenceScheduleTime]


class FlowControlAction(NameValueStrEnum):
    CONTINUE = "CONTINUE"
    PAUSE = "PAUSE"
    TERMINATE = "TERMINATE"


class SequenceFlowControlConfig(BaseModel):
    meeting_booked: FlowControlAction = FlowControlAction.TERMINATE
    email_unsubscribed: FlowControlAction = FlowControlAction.TERMINATE


class SequenceV2(TableModel):
    table_name = TableName.sequence_v2
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    owner_user_id: Column[UUID]
    participants: JsonColumn[list[SequenceParticipant] | None] = None
    visibility: Column[SequenceVisibility]
    status: Column[SequenceStatus]
    cloned_from_sequence_id: Column[UUID | None] = None
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    schedule: JsonColumn[SequenceV2Schedule]
    is_blueprint: Column[bool] = False
    unsubscription_group_id: Column[UUID | None] = None
    last_activated_at: Column[ZoneRequiredDateTime | None] = None
    flow_control_config: JsonColumn[SequenceFlowControlConfig | None] = None
    enable_pixel_tracking: Column[bool] = True


class SequenceV2Update(TableBoundedModel[SequenceV2]):
    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    owner_user_id: UnsetAware[UUID] = UNSET
    participants: UnsetAware[list[SequenceParticipant] | None] = UNSET
    visibility: UnsetAware[SequenceVisibility] = UNSET
    updated_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    updated_by_user_id: UnsetAware[UUID] = UNSET
    schedule: UnsetAware[SequenceV2Schedule] = UNSET
    unsubscription_group_id: UnsetAware[UUID | None] = UNSET
    enable_pixel_tracking: UnsetAware[bool] = UNSET


class StepFlowControlConfig(BaseModel):
    flow_control_action: FlowControlAction
    # Next step id is not currently used, added here to support future branching use cases
    next_step_id: UUID | None = None


class AutoMailFlowControlConfig(BaseModel):
    step: Literal[SequenceStepType.AUTO_EMAIL]
    email_replied: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.TERMINATE
    )
    email_no_response: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )


class ManualEmailFlowControlConfig(BaseModel):
    step: Literal[SequenceStepType.MANUAL_EMAIL]
    email_replied: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.TERMINATE
    )
    email_no_response: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )


class CallTaskFlowControlConfig(BaseModel):
    step: Literal[SequenceStepType.CALL_TASK]
    call_answered_interested: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.TERMINATE
    )
    call_answered_not_interested: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.TERMINATE
    )
    call_answered_call_back_later: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.PAUSE
    )
    call_answered_wrong_number: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.PAUSE
    )
    call_answered_meeting_scheduled: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.TERMINATE
    )
    call_answered_no_answer: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )
    call_answered_left_voicemail: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )


class LinkedInMessageTaskFlowControlConfig(BaseModel):
    step: Literal[SequenceStepType.LINKEDIN_MESSAGE_TASK]
    task_completed: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )


class LinkedInConnectionRequestTaskFlowControlConfig(BaseModel):
    step: Literal[SequenceStepType.LINKEDIN_CONNECTION_REQUEST_TASK]
    task_completed: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )


class LinkedInInMailTaskFlowControlConfig(BaseModel):
    step: Literal[SequenceStepType.LINKEDIN_INMAIL_TASK]
    task_completed: StepFlowControlConfig = StepFlowControlConfig(
        flow_control_action=FlowControlAction.CONTINUE
    )


FlowControlConfig = Annotated[
    AutoMailFlowControlConfig
    | ManualEmailFlowControlConfig
    | CallTaskFlowControlConfig
    | LinkedInMessageTaskFlowControlConfig
    | LinkedInConnectionRequestTaskFlowControlConfig
    | LinkedInInMailTaskFlowControlConfig,
    Field(discriminator="step"),
]


class SequenceStepV2(TableModel):
    table_name = TableName.sequence_step_v2
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    sequence_id: Column[UUID]
    is_first_step: Column[bool]
    next_step_id: Column[UUID | None] = None
    delay_minutes: Column[int]
    type: Column[SequenceStepType]
    manual_flow_control: Column[SequenceStepManualFlowControl | None] = None
    manual_task_priority: Column[str | None] = None
    flow_control_config: JsonColumn[FlowControlConfig | None] = None
    support_ab_test: Column[bool]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class EmailStepContent(BaseModel):
    type: Literal["email"]
    subject: str
    body: str
    attachment_ids: list[UUID] | None = None
    include_email_signature: bool = False


class EmailTaskStepContent(BaseModel):
    type: Literal["manual_email"]
    subject: str
    body: str
    attachment_ids: list[UUID] | None = None
    include_email_signature: bool = False
    priority: Literal["low", "medium", "high"] = "medium"


class CallTaskStepContent(BaseModel):
    type: Literal["call"]
    title: str
    description: str
    priority: Literal["low", "medium", "high"] = "medium"


class LinkedInMessageTaskStepContent(BaseModel):
    type: Literal["linkedin"]
    message: str
    note: str
    priority: Literal["low", "medium", "high"] = "medium"


class LinkedInConnectionRequestTaskStepContent(BaseModel):
    type: Literal["linkedin_connection_request"]
    message: str
    note: str
    priority: Literal["low", "medium", "high"] = "medium"


class LinkedInInMailTaskStepContent(BaseModel):
    type: Literal["linkedin_inmail_request"]
    message: str
    note: str
    priority: Literal["low", "medium", "high"] = "medium"


# Discriminated union for step content based on step type
StepContent = Annotated[
    EmailStepContent
    | EmailTaskStepContent
    | CallTaskStepContent
    | LinkedInMessageTaskStepContent
    | LinkedInConnectionRequestTaskStepContent
    | LinkedInInMailTaskStepContent,
    Field(discriminator="type"),
]


class SequenceStepVariantStatus(StrEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"


class SequenceStepVariant(TableModel):
    table_name = TableName.sequence_step_variant
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    sequence_step_id: Column[UUID]
    template_id: Column[UUID | None] = None
    content: JsonColumn[StepContent]
    status: Column[SequenceStepVariantStatus]
    reply_to_previous_thread: Column[bool] = False
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class SequenceEnrollmentStatus(NameValueStrEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    FAILED = "FAILED"
    EXITED = "EXITED"
    REMOVED = "REMOVED"
    PENDING = "PENDING"

    @classmethod
    def get_terminated_statuses(cls) -> list[SequenceEnrollmentStatus]:
        return [cls.EXITED, cls.REMOVED]

    def is_terminated(self) -> bool:
        return self in self.get_terminated_statuses()

    @classmethod
    def map_to_display_status(
        cls,
        status: SequenceEnrollmentStatus,
        exited_by_reference_id: UUID | None = None,
    ) -> SequenceEnrollmentDisplayStatus:
        if status == SequenceEnrollmentStatus.INACTIVE:
            return SequenceEnrollmentDisplayStatus.PAUSED
        elif status == SequenceEnrollmentStatus.REMOVED or (
            status == SequenceEnrollmentStatus.EXITED and exited_by_reference_id is None
        ):
            return SequenceEnrollmentDisplayStatus.COMPLETED
        elif (
            status == SequenceEnrollmentStatus.EXITED
            and exited_by_reference_id is not None
        ):
            return SequenceEnrollmentDisplayStatus.CONVERTED
        elif status == SequenceEnrollmentStatus.FAILED:
            return SequenceEnrollmentDisplayStatus.FAILED
        else:
            # Covers pending, active
            return SequenceEnrollmentDisplayStatus.ENROLLED


class SequenceEnrollmentDisplayStatus(NameValueStrEnum):
    ENROLLED = "ENROLLED"
    PAUSED = "PAUSED"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CONVERTED = "CONVERTED"


class SequenceEnrollmentExitedByReferenceIdType(NameValueStrEnum):
    EMAIL_ACCOUNT = "EMAIL_ACCOUNT"
    MEETING = "MEETING"
    GLOBAL_MESSAGE = "GLOBAL_MESSAGE"


class SequenceEnrollmentExitReasonCode(NameValueStrEnum):
    COMPLETED = "COMPLETED"
    USER_EXITED = "USER_EXITED"
    EMAIL_REPLIED = "EMAIL_REPLIED"
    MEETING_SCHEDULED = "MEETING_SCHEDULED"
    EMAIL_UNSUBSCRIBED = "EMAIL_UNSUBSCRIBED"
    EMAIL_ACCOUNT_DISABLED = "EMAIL_ACCOUNT_DISABLED"
    EMAIL_SEND_FAILED = "EMAIL_SEND_FAILED"
    EMAIL_BOUNCE_DETECTED = "EMAIL_BOUNCE_DETECTED"
    EMAIL_ACCOUNT_UNAVAILABLE = "EMAIL_ACCOUNT_UNAVAILABLE"


class SequenceEnrollment(TableModel):
    table_name = TableName.sequence_enrollment
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    sequence_id: Column[UUID]
    email_account_id: Column[UUID | None] = None
    email_account_pool_id: Column[UUID | None] = None
    contact_id: Column[UUID]
    account_id: Column[UUID | None] = None
    email: Column[str | None] = None
    domain_object_list_id: Column[UUID | None] = None
    status: Column[SequenceEnrollmentStatus]
    current_step_id: Column[UUID | None] = None
    organization_id: Column[UUID]
    enrolled_at: Column[ZoneRequiredDateTime]
    enrolled_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime]
    updated_by_user_id: Column[UUID | None] = None
    exited_at: Column[ZoneRequiredDateTime | None] = None
    exited_by_user_id: Column[UUID | None] = None
    exited_by_reference_id: Column[UUID | None] = None
    exited_by_reference_id_type: Column[str | None] = None
    exited_reason: Column[SequenceEnrollmentExitReasonCode | None] = None
    workflow_id: Column[str | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class SequenceStepExecutionStatus(NameValueStrEnum):
    QUEUED = "QUEUED"
    SENT = "SENT"
    FAILED = "FAILED"
    TERMINATED = "TERMINATED"
    TASK_CREATED = "TASK_CREATED"
    TASK_COMPLETED = "TASK_COMPLETED"
    TASK_COMPLETED_WITH_PAUSED = "TASK_COMPLETED_WITH_PAUSED"


class SequenceStepExecutionResultEntityType(NameValueStrEnum):
    TASK = "TASK"
    GLOBAL_MESSAGE = "GLOBAL_MESSAGE"


class SequenceStepExecution(TableModel):
    table_name = TableName.sequence_step_execution
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    sequence_id: Column[UUID]
    sequence_step_id: Column[UUID]
    sequence_step_variant_id: Column[UUID]
    sequence_enrollment_id: Column[UUID]
    contact_id: Column[UUID | None] = None
    contact_owner_user_id: Column[UUID | None] = None
    mailbox_id: Column[UUID | None] = None
    global_thread_id: Column[UUID | None] = None
    global_message_id: Column[UUID | None] = None
    status: Column[SequenceStepExecutionStatus]
    is_retry: Column[bool | None] = False
    scheduled_at: Column[ZoneRequiredDateTime | None] = None
    executed_at: Column[ZoneRequiredDateTime | None] = None
    error_code: Column[SequenceErrorCode | None] = None
    error_detail: Column[str | None] = None
    result_entity_id: Column[UUID | None] = None
    result_entity_type: Column[SequenceStepExecutionResultEntityType | None] = None
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None


class SequenceEnrollmentStepVariantAssociation(TableModel):
    table_name = TableName.sequence_enrollment_step_variant_association
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    sequence_enrollment_id: Column[UUID]
    sequence_step_id: Column[UUID]
    sequence_step_variant_id: Column[UUID]
    organization_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None


class SequenceEnrollmentRunStatus(StrEnum):
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class SequenceEnrollmentRunMode(StrEnum):
    SYNC = "SYNC"
    ASYNC = "ASYNC"


class SequenceEnrollmentRun(TableModel):
    table_name = TableName.sequence_enrollment_run
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    sequence_id: Column[UUID]
    mode: Column[SequenceEnrollmentRunMode]
    workflow_id: Column[str | None] = None
    status: Column[SequenceEnrollmentRunStatus]
    created_by_user_id: Column[UUID]
    created_at: Column[ZoneRequiredDateTime]


class NumWarningsAndFailuresByEnrollmentRunId(DBModel):
    enrollment_run_id: UUID
    num_successes: int
    num_warnings: int
    num_failures: int


class SequenceEnrollmentContactStatus(StrEnum):
    ENROLLED = "ENROLLED"
    WARNING = "WARNING"
    FAILED = "FAILED"


class SequenceEnrollmentContact(TableModel):
    table_name = TableName.sequence_enrollment_contact
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    enrollment_run_id: Column[UUID]
    contact_id: Column[UUID]
    account_id: Column[UUID | None] = None
    email: Column[str | None] = None
    status: Column[SequenceEnrollmentContactStatus]
    warning_reasons: Column[list[str] | None] = None
    fail_reasons: Column[list[str] | None] = None
    description: Column[str | None] = None
