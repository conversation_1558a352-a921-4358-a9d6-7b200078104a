"""add published_at to reporting_report

Revision ID: add_published_at_to_reporting_report
Revises: f7a301443625
Create Date: 2025-01-30 12:00:00.000000+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "add_published_at_to_reporting_report"
down_revision: str | tuple[str, ...] | None = "f7a301443625"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE reporting_report
        ADD COLUMN published_at TIMESTAMPTZ NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE reporting_report
        DROP COLUMN IF EXISTS published_at;
        """
    )
