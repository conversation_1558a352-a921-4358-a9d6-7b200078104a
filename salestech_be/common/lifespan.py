from httpx import AsyncClient
from starlette.requests import Request

from salestech_be.common.exception.exception import FalkorDBUnavailableError
from salestech_be.db.dbengine.core import (
    DatabaseEngine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.falkordb.falkordb_factory import FalkorDBConnectionManager


def get_db_engine(request: Request) -> DatabaseEngine:
    """Retrieves DB Connection from app context singleton."""
    return request.app.state.app_context.main_db  # type: ignore[no-any-return]


def get_falkordb_connection_manager(request: Request) -> FalkorDBConnectionManager:
    """Retrieves FalkorDB Client instance from app context singleton.

    Raises:
        FalkorDBUnavailableError: If the FalkorDB connection was not established during startup.
    """
    manager = request.app.state.app_context.falkordb_conn_mgr
    if manager is None:
        raise FalkorDBUnavailableError(
            "FalkorDB connection manager is unavailable. Connection likely failed during application startup."
        )
    # The type ignore below might be removable now depending on how AppContex<PERSON> is typed upstream
    return manager  # type: ignore[no-any-return]


def get_http_aclient(request: Request) -> AsyncClient:
    """Retrieves HTTP AsyncClient instance from app context singleton."""
    return request.app.state.app_context.http_aclient  # type: ignore[no-any-return]
