SELECT
    p.*,
    COALESCE(NULLIF(TRIM(CONCAT_WS(' ', u.first_name, u.last_name)), ''), '') as owner_display_name,
    a.display_name as account_display_name,
    psslv.outcome_state,
    COALESCE(slv_stage.mapped_output_value_id, p.stage_id) AS effective_stage_id,
    CASE
      WHEN slv_stage.mapped_output_value_id IS NOT NULL THEN (SELECT display_value FROM select_list_value  WHERE id = slv_stage.mapped_output_value_id)
      ELSE slv_stage.display_value
    END AS effective_stage_display_value
FROM pipeline  AS p
LEFT JOIN select_list_value  AS slv_stage ON p.stage_id = slv_stage.id AND p.organization_id = slv_stage.organization_id
LEFT JOIN pipeline_stage_select_list_value_metadata AS psslv ON psslv.select_list_value_id = p.stage_id AND p.organization_id = psslv.organization_id
LEFT JOIN public.user AS u ON u.id = p.owner_user_id
LEFT JOIN account AS a ON a.id = p.account_id AND a.organization_id = p.organization_id
WHERE p.archived_at IS NULL
