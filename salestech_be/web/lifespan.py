import asyncio
import logging  # noqa
import signal
import time
import traceback
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager
from typing import Any, cast

import anyio
from fastapi import BackgroundTasks, FastAPI
from prometheus_fastapi_instrumentator.instrumentation import (
    PrometheusFastApiInstrumentator,
)
from redis import ConnectionError as RedisConnectionError

from salestech_be.app_context.app_context import AppContext, LifeSpanService
from salestech_be.common.http_aclient import get_http_aclient
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.activity.service.activity_service import (
    get_activity_service_general,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.business_process.sales_methodology.service import (
    get_business_process_service,
)
from salestech_be.core.calendar.user_calendar_schedule_service import (
    get_user_calendar_schedule_service_by_db_engine,
)
from salestech_be.core.calendar.user_calendar_webhook_service import (
    get_user_calendar_webhook_service_by_db_engine,
)
from salestech_be.core.chat.service.chat_history_query_service import (
    get_chat_history_query_service,
)
from salestech_be.core.chat.service.chat_history_service import get_chat_history_service
from salestech_be.core.chat.service.chat_service import get_chat_service
from salestech_be.core.citation.service.citation_query_service import (
    get_citation_query_service,
)
from salestech_be.core.comment.service.comment_service import (
    get_comment_service_from_engine,
)
from salestech_be.core.contact.service.contact_enrichment_service import (
    get_contact_enrichment_service,
)
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import get_crm_ai_rec_service
from salestech_be.core.custom_object.service.association_service import (
    get_association_service,
)
from salestech_be.core.custom_object.service.custom_object_query_service import (
    get_custom_object_query_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    get_custom_object_service,
)
from salestech_be.core.data.service.query_service import get_domain_object_query_service
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    get_domain_object_list_query_service_by_db_engine,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    get_domain_object_list_service,
)
from salestech_be.core.email.account.service_v2 import (
    get_email_account_service_v2_by_db_engine,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    get_global_thread_query_service,
)
from salestech_be.core.email.global_email.global_thread_service import (
    get_global_thread_service_by_db_engine,
)
from salestech_be.core.email.insight.email_insight_service import (
    get_email_insight_service_by_db,
)
from salestech_be.core.email.outbound_domain.service import (
    get_outbound_domain_service_general,
)
from salestech_be.core.email.pool.service import (
    get_email_account_pool_service_general,
)
from salestech_be.core.email.service.imap_syncing_service import (
    get_imap_syncing_service_general,
)
from salestech_be.core.event_tracking.event_tracking_receiver_service import (
    get_event_tracking_msk_producer,
    get_event_tracking_receiver_service,
)
from salestech_be.core.event_tracking.event_tracking_service import (
    get_event_tracking_service,
)
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.goal.service.goal_service import goal_service_by_db_engine
from salestech_be.core.imports.service.crm_sync_service import (
    get_crm_sync_service_from_db_engine,
)
from salestech_be.core.imports.service.import_csv_job_review_service import (
    get_import_csv_job_review_service,
)
from salestech_be.core.job.service.job_runner_service import (
    job_runner_service_from_engine,
)
from salestech_be.core.job.service.job_service import job_service_from_engine
from salestech_be.core.meeting.meeting_agent_service import (
    get_meeting_agent_service_from_engine,
)
from salestech_be.core.meeting.meeting_realtime_service import (
    meeting_realtime_service_by_db_engine,
)
from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.core.meeting.meeting_share_service import (
    get_meeting_share_service_general,
)
from salestech_be.core.meeting.service.meeting_query_service import (
    get_meeting_query_service,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    get_select_list_service,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    get_pipeline_stage_select_list_service,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    get_stage_criteria_service,
)
from salestech_be.core.note.service.note_service import (
    get_note_service_general,
)
from salestech_be.core.notification.service.notification_service import (
    get_notification_service_by_db_engine,
)
from salestech_be.core.pipeline.service.pipeline_intel_service import (
    get_pipeline_intel_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    get_pipeline_qualification_property_service,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services import (
    get_competitor_service,
    get_decision_criteria_item_service,
    get_decision_process_item_service,
    get_identified_pain_item_service,
    get_metric_item_service,
    get_paper_process_item_service,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    get_sequence_query_service_by_db,
)
from salestech_be.core.sequence.service.sequence_service import (
    get_sequence_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_step_service import (
    get_sequence_step_service_by_db_engine,
)
from salestech_be.core.stage_criteria.service.criteria_library_service import (
    CriteriaLibraryService,
)
from salestech_be.core.stage_criteria.stage_criteria_service_v2 import (
    StageCriteriaServiceV2,
)
from salestech_be.core.task.service.task_v2_service import (
    get_task_v2_service_general,
)
from salestech_be.core.tracker.service.tracker_query_service import (
    tracker_query_service_factory,
)
from salestech_be.core.tracker.service.tracker_service import tracker_service_factory
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.core.user.signature.service import get_signature_service
from salestech_be.core.user_feedback.service import get_user_feedback_service
from salestech_be.core.variable.variable_service import get_variable_service
from salestech_be.core.view_management.service.view_management_service import (
    get_view_management_service,
)
from salestech_be.core.workflow.service.workflow_trigger_service import (
    get_workflow_trigger_service_by_db_engine,
)
from salestech_be.db.dao.stage_criteria_v2_repository import (
    CriteriaItemRepositoryV2,
    StageExitCriteriaRepositoryV2,
    StageListEntranceCriteriaRepositoryV2,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.falkordb.falkordb_factory import get_falkordb_connection_manager
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.ree_logging import get_logger
from salestech_be.search.es.search.client_utils import get_es_search_client
from salestech_be.search.search.search_service import get_search_service
from salestech_be.services.auth.encryptions import get_event_tracking_encryption_manager
from salestech_be.settings import settings
from salestech_be.util.asyncio_util.adapter import (
    run_in_pool,
    worker_threadpool_limiter,
)
from salestech_be.web.api.calendar.service import get_user_calendar_service_by_db_engine
from salestech_be.web.api.connect.service import (
    get_user_integration_service_by_db_engine,
)
from salestech_be.web.api.crm_integrity.service import (
    get_crm_integrity_service_by_db_engine,
)
from salestech_be.web.api.email.common.email_webhook_service import (
    get_email_webhook_service_by_db_engine,
)
from salestech_be.web.api.propagation_rule.service import get_propagation_rule_service
from salestech_be.web.api.prospecting.company.service import (
    get_company_service_with_db_engine,
)
from salestech_be.web.api.prospecting.people.service import (
    get_people_service_with_db_engine,
)
from salestech_be.web.api.schedule.service import (
    get_event_schedule_service_by_db_engine,
)
from salestech_be.web.api.user.platform_credential.service import (
    get_user_platform_credential_service,
)
from salestech_be.web.api.user_invite.service import (
    get_user_invite_service_by_db_engine,
)
from salestech_be.web.api.webhook.service.nylas_webhook_service import (
    get_nylas_webhook_service_by_db_engine,
)
from salestech_be.web.api.workflow.block.service import (
    get_workflow_block_service_by_db_engine,
)
from salestech_be.web.api.workflow.edge.service import (
    get_workflow_edge_service_by_db_engine,
)
from salestech_be.web.api.workflow.node.service import (
    get_workflow_node_service_by_db_engine,
)
from salestech_be.web.api.workflow.run.service import (
    get_workflow_run_service_by_db_engine,
)
from salestech_be.web.api.workflow.run_node.service import (
    get_workflow_run_node_service_by_db_engine,
)
from salestech_be.web.api.workflow.snapshot.service import (
    get_workflow_snapshot_service_by_db_engine,
)
from salestech_be.web.api.workflow.workflow.service import (
    get_workflow_service_by_db_engine,
)

# Configure logging for apscheduler, should NOT be used for loggers
logging.basicConfig(level=logging.WARNING)
logging.getLogger("apscheduler").setLevel(logging.WARNING)

logger = get_logger()


# Anyio System ThreadPool: FastAPI uses this for synchronous routes/dep functions
# When you declare a path operation function with normal def instead of async def, it is run in an external threadpool that is then awaited, instead of being called directly (as it would block the server).
# The same applies for dependencies. If a dependency is a standard def function instead of async def, it is run in the external threadpool.
def init_default_anyio_limiter() -> anyio.CapacityLimiter:
    # Directly create a CapacityLimiter with the desired size.
    # This avoids issues with differing APIs across anyio versions for getting the default limiter.
    return anyio.CapacityLimiter(settings.default_main_thread_pool_size)
    # Setting total_tokens might not be needed if created directly with the size,
    # but keeping it in case it influences behavior or is used elsewhere.
    # system_threadpool_limiter.total_tokens = settings.default_main_thread_pool_size


def set_app_context(app: FastAPI, app_context: AppContext) -> None:
    app.state.app_context = app_context


def get_app_context(app: FastAPI) -> AppContext | None:
    try:
        return cast(AppContext, app.state.app_context)
    except KeyError:
        return None


async def setup_app_context(app: FastAPI) -> None:
    # set up DB
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()



    # Initialize FalkorDB connection manager gracefully
    falkordb_conn_mgr = None
    try:
        falkordb_conn_mgr = get_falkordb_connection_manager()
        # Optional: Add a more robust check if available, e.g., ping
        # await falkordb_conn_mgr.ping()
    # Catch specific connection errors from redis client used by falkordb
    except RedisConnectionError as e:
        logger.error(
            f"Failed to initialize FalkorDB connection ({e}). "
            f"FalkorDB functionality will be unavailable.",
            exc_info=True,
        )
        # falkordb_conn_mgr remains None, allowing setup to continue
    except Exception as e:  # Catch other potential init errors
        logger.error(
            f"An unexpected error occurred during FalkorDB initialization ({e}). "
            f"FalkorDB functionality will be unavailable.",
            exc_info=True,
        )
        # falkordb_conn_mgr remains None, allowing setup to continue

    # temporal client
    temporal_client = await get_temporal_client()

    # kafka producer for event tracking (e.g. pixel / open-rate, link clicks)
    event_tracking_msk_producer = get_event_tracking_msk_producer()
    await event_tracking_msk_producer.start()

    # set up lifespan-scope services (singleton)
    background_tasks = BackgroundTasks()

    domain_object_query_service = get_domain_object_query_service(
        db_engine=engine, falkordb_conn_mgr=falkordb_conn_mgr
    )
    account_service = get_account_service(
        db_engine=engine,
    )
    people_service = get_people_service_with_db_engine(
        db_engine=engine,
    )
    company_service = get_company_service_with_db_engine(
        db_engine=engine,
    )
    job_runner_service = job_runner_service_from_engine(
        engine=engine,
    )
    pipeline_service = get_pipeline_service(db_engine=engine)
    pipeline_stage_select_list_service = get_pipeline_stage_select_list_service(
        engine=engine
    )
    es_search_client = get_es_search_client()
    search_service = get_search_service(search_client=es_search_client)
    criteria_library_service = CriteriaLibraryService()
    criteria_item_repository = CriteriaItemRepositoryV2(engine=engine)
    stage_exit_criteria_repository = StageExitCriteriaRepositoryV2(engine=engine)
    stage_list_entrance_criteria_repository = StageListEntranceCriteriaRepositoryV2(
        engine=engine
    )

    lifespan_service = LifeSpanService(
        user_calendar_webhook_service=get_user_calendar_webhook_service_by_db_engine(
            engine
        ),
        email_webhook_service=get_email_webhook_service_by_db_engine(db_engine=engine),
        user_calendar_service=get_user_calendar_service_by_db_engine(
            db_engine=engine, background_tasks=background_tasks
        ),
        user_calendar_schedule_service=get_user_calendar_schedule_service_by_db_engine(
            db_engine=engine
        ),
        nylas_webhook_service=get_nylas_webhook_service_by_db_engine(db_engine=engine),
        user_service=get_user_service_general(db_engine=engine),
        user_platform_credential_service=get_user_platform_credential_service(
            db_engine=engine
        ),
        user_integration_service=get_user_integration_service_by_db_engine(
            db_engine=engine
        ),
        user_invite_service=get_user_invite_service_by_db_engine(db_engine=engine),
        crm_ai_rec_service=get_crm_ai_rec_service(db_engine=engine),
        pipeline_intel_service=get_pipeline_intel_service(db_engine=engine),
        pipeline_qualification_property_service=get_pipeline_qualification_property_service(
            db_engine=engine
        ),
        identified_pain_item_service=get_identified_pain_item_service(db_engine=engine),
        decision_criteria_item_service=get_decision_criteria_item_service(
            db_engine=engine
        ),
        metric_item_service=get_metric_item_service(db_engine=engine),
        paper_process_item_service=get_paper_process_item_service(db_engine=engine),
        competitor_service=get_competitor_service(db_engine=engine),
        decision_process_item_service=get_decision_process_item_service(
            db_engine=engine
        ),
        pipeline_stage_select_list_service=pipeline_stage_select_list_service,
        stage_criteria_service=get_stage_criteria_service(
            engine=engine, domain_object_query_service=domain_object_query_service
        ),
        stage_criteria_service_v2=StageCriteriaServiceV2(
            domain_object_query_service=domain_object_query_service,
            pipeline_stage_select_list_service=pipeline_stage_select_list_service,
            criteria_library_service=criteria_library_service,
            criteria_item_repository=criteria_item_repository,
            stage_exit_criteria_repository=stage_exit_criteria_repository,
            stage_list_entrance_criteria_repository=stage_list_entrance_criteria_repository,
        ),
        select_list_service=get_select_list_service(engine=engine),
        event_schedule_service=get_event_schedule_service_by_db_engine(
            db_engine=engine
        ),
        meeting_service=meeting_service_factory_general(db_engine=engine),
        meeting_realtime_service=meeting_realtime_service_by_db_engine(
            db_engine=engine
        ),
        meeting_query_service=get_meeting_query_service(db_engine=engine),
        meeting_agent_service=get_meeting_agent_service_from_engine(db_engine=engine),
        email_insight_service=get_email_insight_service_by_db(db_engine=engine),
        account_service=account_service,
        contact_service=get_contact_service(db_engine=engine),
        contact_query_service=get_contact_query_service(db_engine=engine),
        domain_object_query_service=domain_object_query_service,
        view_management_service=get_view_management_service(
            db_engine=engine, domain_object_query_service=domain_object_query_service
        ),
        activity_service=get_activity_service_general(db_engine=engine),
        people_service=people_service,
        company_service=company_service,
        email_outbound_domain_service=get_outbound_domain_service_general(
            engine=engine
        ),
        email_account_pool_service=get_email_account_pool_service_general(
            engine=engine
        ),
        email_account_service_v2=get_email_account_service_v2_by_db_engine(
            db_engine=engine
        ),
        job_runner_service=job_runner_service,
        note_service=get_note_service_general(db_engine=engine),
        goal_service=goal_service_by_db_engine(db_engine=engine),
        feature_flag_service=get_feature_flag_service(),
        imap_syncing_service=get_imap_syncing_service_general(engine=engine),
        crm_sync_service=get_crm_sync_service_from_db_engine(engine=engine),
        import_csv_job_review_service=get_import_csv_job_review_service(
            db_engine=engine,
            temporal_client=temporal_client,
        ),
        job_service=job_service_from_engine(engine=engine),
        langfuse_prompt_service=get_langfuse_prompt_service(),
        # sequence
        sequence_service=get_sequence_service_by_db_engine(engine=engine),
        sequence_query_service=get_sequence_query_service_by_db(db_engine=engine),
        sequence_step_service=get_sequence_step_service_by_db_engine(engine=engine),
        sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
            db_engine=engine
        ),
        workflow_node_service=get_workflow_node_service_by_db_engine(engine=engine),
        workflow_snapshot_service=get_workflow_snapshot_service_by_db_engine(
            engine=engine,
            client=temporal_client,
        ),
        workflow_block_service=get_workflow_block_service_by_db_engine(engine=engine),
        workflow_edge_service=get_workflow_edge_service_by_db_engine(engine=engine),
        workflow_run_service=get_workflow_run_service_by_db_engine(engine=engine),
        workflow_run_node_service=get_workflow_run_node_service_by_db_engine(
            engine=engine
        ),
        workflow_service=get_workflow_service_by_db_engine(
            engine=engine,
            client=temporal_client,
        ),
        workflow_trigger_service=get_workflow_trigger_service_by_db_engine(
            db_engine=engine
        ),
        propagation_rule_service=get_propagation_rule_service(
            db_engine=engine,
            domain_object_query_service=domain_object_query_service,
        ),
        pipeline_service=pipeline_service,
        meeting_share_service=get_meeting_share_service_general(db_engine=engine),
        notification_service=get_notification_service_by_db_engine(db_engine=engine),
        crm_integrity_service=get_crm_integrity_service_by_db_engine(db_engine=engine),
        tracker_service=tracker_service_factory(db_engine=engine),
        tracker_query_service=tracker_query_service_factory(engine),
        variable_service=get_variable_service(db_engine=engine),
        custom_object_service=get_custom_object_service(db_engine=engine),
        association_service=get_association_service(db_engine=engine),
        domain_object_list_service=get_domain_object_list_service(db_engine=engine),
        custom_object_query_service=get_custom_object_query_service(db_engine=engine),
        search_service=search_service,
        chat_service=get_chat_service(db_engine=engine),
        chat_history_service=get_chat_history_service(db_engine=engine),
        chat_history_query_service=get_chat_history_query_service(db_engine=engine),
        citation_query_service=get_citation_query_service(db_engine=engine),
        business_process_service=get_business_process_service(db_engine=engine),
        signature_service=get_signature_service(db_engine=engine),
        event_tracking_service=get_event_tracking_service(db_engine=engine),
        event_tracking_receiver_service=get_event_tracking_receiver_service(
            encryption_manager=get_event_tracking_encryption_manager(),
            msk_producer=event_tracking_msk_producer,
        ),
        event_tracking_msk_producer=event_tracking_msk_producer,
        task_v2_service=get_task_v2_service_general(db_engine=engine),
        comment_service=get_comment_service_from_engine(db_engine=engine),
        user_feedback_service=get_user_feedback_service(db_engine=engine),
        global_thread_service=get_global_thread_service_by_db_engine(db_engine=engine),
        global_thread_query_service=get_global_thread_query_service(db_engine=engine),
        contact_enrichment_service=get_contact_enrichment_service(db_engine=engine),
        domain_object_list_query_service=get_domain_object_list_query_service_by_db_engine(
            db_engine=engine
        ),
    )

    app_context = AppContext(
        main_db=engine,
        falkordb_conn_mgr=falkordb_conn_mgr,
        http_aclient=get_http_aclient(),
        lifespan_service=lifespan_service,
        es_search_client=es_search_client,
    )
    set_app_context(app=app, app_context=app_context)


async def close_app_context(app: FastAPI) -> None:
    app_context = get_app_context(app=app)
    if app_context:
        await app_context.close()


def setup_opentelemetry(app: FastAPI) -> None:  # pragma: no cover
    """Enables opentelemetry instrumentation.

    :param app: current application.
    """
    from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
    from opentelemetry.instrumentation.redis import RedisInstrumentor
    from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
    from opentelemetry.sdk.resources import (
        DEPLOYMENT_ENVIRONMENT,
        SERVICE_NAME,
        TELEMETRY_SDK_LANGUAGE,
        Resource,
    )
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.trace import set_tracer_provider

    if not settings.opentelemetry_endpoint:
        return

    tracer_provider = TracerProvider(
        resource=Resource(
            attributes={
                SERVICE_NAME: "salestech_be",
                TELEMETRY_SDK_LANGUAGE: "python",
                DEPLOYMENT_ENVIRONMENT: settings.environment,
            },
        ),
    )

    tracer_provider.add_span_processor(
        BatchSpanProcessor(
            OTLPSpanExporter(
                endpoint=settings.opentelemetry_endpoint,
                insecure=True,
            ),
        ),
    )

    excluded_endpoints = [
        app.url_path_for("health_check"),
        app.url_path_for("openapi"),
        app.url_path_for("swagger_ui_html"),
        app.url_path_for("swagger_ui_redirect"),
        app.url_path_for("redoc_html"),
        "/metrics",
    ]

    FastAPIInstrumentor().instrument_app(
        app,
        tracer_provider=tracer_provider,
        excluded_urls=",".join(excluded_endpoints),
    )
    RedisInstrumentor().instrument(
        tracer_provider=tracer_provider,
    )
    SQLAlchemyInstrumentor().instrument(
        tracer_provider=tracer_provider,
        engine=app.state.db_engine.sync_engine,
    )

    set_tracer_provider(tracer_provider=tracer_provider)


def stop_opentelemetry(app: FastAPI) -> None:  # pragma: no cover
    """Disables opentelemetry instrumentation.

    :param app: current application.
    """
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
    from opentelemetry.instrumentation.redis import RedisInstrumentor
    from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor

    if not settings.opentelemetry_endpoint:
        return

    FastAPIInstrumentor().uninstrument_app(app)
    RedisInstrumentor().uninstrument()
    SQLAlchemyInstrumentor().uninstrument()


def setup_prometheus(app: FastAPI) -> None:  # pragma: no cover
    """Enables prometheus integration.

    :param app: current application.
    """
    # Corrected import path used above

    PrometheusFastApiInstrumentator(should_group_status_codes=False).instrument(
        app,
    ).expose(app, should_gzip=True, name="prometheus_metrics")


async def _gauge_event_loop(interval: float = 1.0) -> None:
    while True:
        start_time = time.perf_counter()
        await asyncio.sleep(interval)
        actual_time = time.perf_counter()
        delay = max(actual_time - start_time - interval, 0)
        custom_metric.gauge(
            metric_name="event_loop_delay",
            value=delay,
        )
        custom_metric.gauge(
            metric_name="event_loop_tasks",
            value=len(asyncio.all_tasks()),
        )
        # print(f"Main Loop - Current delay: {delay:.4f} seconds")


ONE_MILLI_SECOND_FLOAT = 0.001


async def _gauge_anyio_threadpool(
    interval: float = 1.0,
    name: str = "worker",
    limiter: anyio.CapacityLimiter = worker_threadpool_limiter,
) -> None:
    while True:
        start_time = time.perf_counter()
        await run_in_pool(time.sleep, ONE_MILLI_SECOND_FLOAT, __anyio_limiter=limiter)  # type: ignore
        actual_time = time.perf_counter()
        delay = max(actual_time - start_time - ONE_MILLI_SECOND_FLOAT, 0)
        custom_metric.gauge(
            metric_name=f"anyio_{name}_delay",
            value=delay,
        )
        utilization = limiter.borrowed_tokens / limiter.total_tokens
        custom_metric.gauge(
            metric_name=f"anyio_{name}_utilization",
            value=utilization,
        )

        await asyncio.sleep(interval)


def print_running_tasks() -> None:
    loop = asyncio.get_running_loop()
    tasks: set[asyncio.Task[Any]] = asyncio.all_tasks(loop)  # type: ignore[explicit-any] # TODO: fix-any-annotation
    logger.info(f"Current running tasks at SIGTERM ({len(tasks)} total):")
    for task in tasks:
        if not task.done():
            # Get task name and stack
            task_name = task.get_name()
            stack = "".join(traceback.format_stack(task.get_stack()[-1]))
            logger.info(f"Task {task_name}: {stack}")


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    task_gauge_event_loop = None
    task_gauge_anyio_worker = None
    task_gauge_anyio_system = None

    loop = asyncio.get_running_loop()
    if settings.enable_event_loop_debug:
        # Set up SIGTERM handler
        loop.add_signal_handler(signal.SIGTERM, print_running_tasks)
        loop.set_debug(True)
        asyncio.get_event_loop().slow_callback_duration = 10.0  # 10 seconds

    # Set up a custom exception handler to properly handle CancelledError
    def handle_cancelled_error(
        loop: asyncio.AbstractEventLoop, context: dict[str, object]
    ) -> None:
        exception = context.get("exception")
        if isinstance(exception, asyncio.CancelledError):
            # Log at error level in JSON format and suppress propagation to avoid the f_lineno error
            logger.error(
                "Asyncio task cancelled",
                extra={
                    "error_type": "task_cancelled",
                    "message": context.get("message", ""),
                    "task": context.get("task", "unknown"),
                    "exception": str(exception) if exception else "None",
                },
            )
            return
        # For all other exceptions, use the default exception handler
        loop.default_exception_handler(context)

    # Register our custom exception handler
    loop.set_exception_handler(handle_cancelled_error)

    try:
        # initialization
        app.middleware_stack = None
        start = time.time()
        await setup_app_context(app=app)
        logger.info(f"time taken to setup_app_context: {time.time() - start}")
        app.middleware_stack = app.build_middleware_stack()

        # resource/capacity config
        system_threadpool_limiter = init_default_anyio_limiter()

        # instrumentation
        # setup_prometheus(app)
        task_gauge_event_loop = asyncio.create_task(
            _gauge_event_loop(), name="_task_gauge_event_loop"
        )  # "main" event loop
        task_gauge_anyio_worker = asyncio.create_task(
            _gauge_anyio_threadpool(name="worker", limiter=worker_threadpool_limiter),
            name="_task_gauge_anyio_worker",
        )  # "anyio" worker threadpool
        task_gauge_anyio_system = asyncio.create_task(
            _gauge_anyio_threadpool(name="system", limiter=system_threadpool_limiter),
            name="_task_gauge_anyio_system",
        )  # "anyio" system threadpool

        logger.info("app context initiated")

        yield
    except Exception:
        logger.exception("Failed to setup lifespan")
        raise
    finally:
        # Remove signal handler
        loop.remove_signal_handler(signal.SIGTERM)

        # cleanup
        await close_app_context(app=app)

        if task_gauge_event_loop:
            task_gauge_event_loop.cancel()
        if task_gauge_anyio_worker:
            task_gauge_anyio_worker.cancel()
        if task_gauge_anyio_system:
            task_gauge_anyio_system.cancel()

        logger.info("app context closed")
