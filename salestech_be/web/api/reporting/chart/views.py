from typing import Annotated
from uuid import UUID

from fastapi import Depends, HTTPException, Query

from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.reporting_repository import ReportingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import ReportingChart
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.reporting.chart.schema import (
    CreateChartRequest,
    ChartDetailResponse,
    ChartListResponse,
    ChartResponse,
    UpdateChartRequest,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.chart")


async def get_reporting_repository(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ReportingRepository:
    return ReportingRepository(engine=db_engine)


@router.get(
    "",
    response_model=ChartListResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_charts(
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
) -> ChartListResponse:
    """List all charts for an organization."""
    charts = await reporting_repository.get_charts(organization_id)

    # Apply pagination
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_charts = charts[start_idx:end_idx]

    return ChartListResponse(
        charts=[
            ChartResponse(
                id=chart.id,
                name=chart.name,
                description=chart.description,
                dashboard_id=UUID("00000000-0000-0000-0000-000000000000"),  # TODO: Fix dashboard_id
                dataset_id=chart.dataset_id,
                created_at=chart.created_at.isoformat(),
                updated_at=chart.updated_at.isoformat() if chart.updated_at else None,
                published_at=chart.published_at.isoformat() if chart.published_at else None,
                layout_config=chart.layout_config,
            )
            for chart in paginated_charts
        ],
        total=len(charts),
        page=page,
        page_size=page_size,
    )


@router.get(
    "/{chart_id}",
    response_model=ChartDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_chart(
    chart_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> ChartDetailResponse:
    """Get a chart by ID."""
    chart = await reporting_repository.find_chart_by_id(chart_id, organization_id)
    if not chart:
        raise HTTPException(status_code=404, detail="Chart not found")

    return ChartDetailResponse(
        id=chart.id,
        name=chart.name,
        description=chart.description,
        dashboard_id=UUID("00000000-0000-0000-0000-000000000000"),  # TODO: Fix dashboard_id
        dataset_id=chart.dataset_id,
        created_at=chart.created_at.isoformat(),
        updated_at=chart.updated_at.isoformat() if chart.updated_at else None,
        published_at=chart.published_at.isoformat() if chart.published_at else None,
        layout_config=chart.layout_config,
    )


@router.post(
    "",
    response_model=ChartDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_chart(
    request: CreateChartRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> ChartDetailResponse:
    """Create a new chart."""
    # Validate that the dashboard exists
    # TODO: Add validation for dashboard_id

    # Validate that the dataset exists
    dataset = await reporting_repository.find_dataset_by_id(request.dataset_id, organization_id)
    if not dataset:
        raise HTTPException(status_code=400, detail="Dataset not found")

    # Create the chart
    now = zoned_utc_now()
    chart = ReportingChart(
        id=UUID(int=0),  # Will be replaced by the database
        name=request.name,
        description=request.description,
        dataset_id=request.dataset_id,
        layout_config=request.layout_config,
        organization_id=organization_id,
        created_at=now,
        created_by_user_id=user_id,
    )

    created_chart = await reporting_repository.insert(chart)

    return ChartDetailResponse(
        id=created_chart.id,
        name=created_chart.name,
        description=created_chart.description,
        dashboard_id=request.dashboard_id,
        dataset_id=created_chart.dataset_id,
        created_at=created_chart.created_at.isoformat(),
        updated_at=created_chart.updated_at.isoformat() if created_chart.updated_at else None,
        published_at=created_chart.published_at.isoformat() if created_chart.published_at else None,
        layout_config=created_chart.layout_config,
    )


@router.put(
    "/{chart_id}",
    response_model=ChartDetailResponse,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def update_chart(
    chart_id: UUID,
    request: UpdateChartRequest,
    organization_id: AnnotatedReevoOrganizationId,
    user_id: AnnotatedReevoUserId,
    reporting_repository: Annotated[ReportingRepository, Depends(get_reporting_repository)],
) -> ChartDetailResponse:
    """Update an existing chart."""
    # Get the existing chart
    chart = await reporting_repository.find_chart_by_id(chart_id, organization_id)
    if not chart:
        raise HTTPException(status_code=404, detail="Chart not found")

    # Update the chart fields
    if request.name is not None:
        chart.name = request.name
    if request.description is not None:
        chart.description = request.description
    if request.layout_config is not None:
        chart.layout_config = request.layout_config

    # Update the audit fields
    chart.updated_at = zoned_utc_now()
    chart.updated_by_user_id = user_id

    # Save the updated chart
    updated_chart = await reporting_repository.update(chart)

    return ChartDetailResponse(
        id=updated_chart.id,
        name=updated_chart.name,
        description=updated_chart.description,
        dashboard_id=UUID("00000000-0000-0000-0000-000000000000"),  # TODO: Fix dashboard_id
        dataset_id=updated_chart.dataset_id,
        created_at=updated_chart.created_at.isoformat(),
        updated_at=updated_chart.updated_at.isoformat() if updated_chart.updated_at else None,
        published_at=updated_chart.published_at.isoformat() if updated_chart.published_at else None,
        layout_config=updated_chart.layout_config,
    )
