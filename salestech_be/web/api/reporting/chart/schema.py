from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.reporting.type.layout import ChartLayoutConfig


class ChartResponse(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    dashboard_id: UUID
    dataset_id: UUID
    created_at: str
    updated_at: str | None = None
    published_at: str | None = None
    layout_config: ChartLayoutConfig | None = None


class ChartDetailResponse(ChartResponse):
    pass


class ChartListResponse(BaseModel):
    charts: list[ChartResponse]
    total: int
    page: int
    page_size: int


class CreateChartRequest(BaseModel):
    name: str
    description: str | None = None
    dashboard_id: UUID
    dataset_id: UUID
    layout_config: ChartLayoutConfig | None = None


class UpdateChartRequest(BaseModel):
    name: str | None = None
    description: str | None = None
    layout_config: ChartLayoutConfig | None = None


class PublishChartRequest(BaseModel):
    """Request to publish a chart (sets published_at to current timestamp)"""
    pass


class UnpublishChartRequest(BaseModel):
    """Request to unpublish a chart (sets published_at to null)"""
    pass


class ChartQueryRequest(BaseModel):
    filters: dict[str, Any] | None = None
